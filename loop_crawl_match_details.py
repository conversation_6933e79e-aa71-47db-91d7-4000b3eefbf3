#!/usr/bin/env python3
"""
Background daemon để crawl match details li<PERSON><PERSON> <PERSON><PERSON><PERSON> sử dụng trong Docker container
"""

import os
import time
import logging
import signal
import sys
from database import SessionLocal, Match, MatchDetail
from fotmob_crawler import crawler
from redis_client import redis_client
from config import settings

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MatchDetailsProcessor:
    def __init__(self):
        self.running = False
        self.interval = int(os.getenv("MATCH_DETAILS_PROCESS_INTERVAL", "60"))
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
        
    def start(self):
        """Start the match details processing loop"""
        # Setup signal handlers
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
        self.running = True
        logger.info(f"Starting match details processor (interval: {self.interval}s)")
        
        # Initial setup
        if not self.ensure_session():
            logger.error("Failed to establish initial session")
            return
            
        # Populate queue if empty
        self.populate_queue_if_empty()
        
        # Main processing loop
        while self.running:
            try:
                self.process_batch()
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"Error in main loop: {e}", exc_info=True)
                time.sleep(min(self.interval, 60))  # Wait at least 60s on error
                
        logger.info("Match details processor stopped")
    
    def ensure_session(self):
        """Ensure crawler session is established"""
        try:
            if not crawler.establish_session():
                logger.warning("Failed to establish session, retrying...")
                time.sleep(5)
                return crawler.establish_session()
            return True
        except Exception as e:
            logger.error(f"Session establishment error: {e}")
            return False
    
    def populate_queue_if_empty(self):
        """Populate Redis queue if it's empty"""
        try:
            queue_length = redis_client.get_queue_length()
            if queue_length == 0:
                logger.info("Queue is empty, populating from database...")
                
                db = SessionLocal()
                try:
                    # Get matches that haven't been crawled yet
                    pending_matches = db.query(Match).filter(
                        Match.details_crawled == False
                    ).limit(1000).all()
                    
                    if pending_matches:
                        match_ids = [m.match_id for m in pending_matches]
                        redis_client.add_matches_batch_to_queue(match_ids, priority=1)
                        logger.info(f"Added {len(match_ids)} matches to queue from database")
                    else:
                        logger.info("No pending matches found in database")
                        
                finally:
                    db.close()
            else:
                logger.info(f"Queue already has {queue_length} matches")
                
        except Exception as e:
            logger.error(f"Error populating queue: {e}")
    
    def process_batch(self):
        """Process a batch of matches from the queue"""
        try:
            # Ensure session is still valid
            if not self.ensure_session():
                logger.error("Session not available, skipping batch")
                return
                
            # Get batch from queue
            batch_size = min(50, max(10, self.interval // 2))  # Adaptive batch size
            match_ids = redis_client.pop_matches_from_queue(batch_size)
            
            if not match_ids:
                logger.info("No matches in queue, checking database...")
                self.populate_queue_if_empty()
                return
                
            logger.info(f"Processing batch of {len(match_ids)} matches")
            
            db = SessionLocal()
            try:
                success_count = 0
                for i, match_id in enumerate(match_ids, 1):
                    try:
                        # Check if already processed
                        existing = db.query(MatchDetail).filter(
                            MatchDetail.match_id == match_id
                        ).first()
                        
                        if existing:
                            logger.info(f"[{i}/{len(match_ids)}] {match_id}: Already in DB, skipping")
                            continue
                            
                        # Crawl match details
                        success = crawler.crawl_match_details(db, match_id)
                        if success:
                            success_count += 1
                            logger.info(f"[{i}/{len(match_ids)}] ✅ {match_id}: Processed successfully")
                        else:
                            logger.warning(f"[{i}/{len(match_ids)}] ❌ {match_id}: Failed to process")
                            
                        # Small delay between requests
                        if i < len(match_ids):
                            time.sleep(1)
                            
                    except Exception as e:
                        logger.error(f"[{i}/{len(match_ids)}] ❌ {match_id}: Error - {e}")
                        
                logger.info(f"Batch completed: {success_count}/{len(match_ids)} successful")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error processing batch: {e}", exc_info=True)

def main():
    """Main entry point"""
    processor = MatchDetailsProcessor()
    try:
        processor.start()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
