#!/usr/bin/env python3
"""
Script để fix luồng match_details và populate Redis queue
"""

import json
import logging
from datetime import datetime, timedelta
from database import SessionLocal, Match, MatchDetail
from fotmob_crawler import crawler
from redis_client import redis_client

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_current_state():
    """Phân tích trạng thái hiện tại"""
    logger.info("🔍 Analyzing current state...")
    
    db = SessionLocal()
    try:
        # Database stats
        total_matches = db.query(Match).count()
        crawled_matches = db.query(Match).filter(Match.details_crawled == True).count()
        pending_matches = db.query(Match).filter(Match.details_crawled == False).count()
        total_details = db.query(MatchDetail).count()
        
        # Redis stats
        queue_length = redis_client.get_queue_length()
        redis_details_count = len(redis_client.list_live_match_ids())
        
        print("📊 Current State:")
        print(f"   Database:")
        print(f"     - Total matches: {total_matches}")
        print(f"     - Details crawled: {crawled_matches}")
        print(f"     - Pending crawl: {pending_matches}")
        print(f"     - Match details in DB: {total_details}")
        print(f"   Redis:")
        print(f"     - Queue length: {queue_length}")
        print(f"     - Match details cached: {redis_details_count}")
        
        # Sample match analysis
        print(f"\n📋 Sample match analysis:")
        recent_matches = db.query(Match).order_by(Match.created_at.desc()).limit(5).all()
        for match in recent_matches:
            try:
                data = json.loads(match.data) if match.data else {}
                status = data.get('status', {}).get('reason', {}).get('short', 'unknown')
                print(f"   - {match.match_id}: {status} (crawled: {match.details_crawled})")
            except:
                print(f"   - {match.match_id}: invalid data (crawled: {match.details_crawled})")
        
        return {
            'total_matches': total_matches,
            'pending_matches': pending_matches,
            'queue_length': queue_length,
            'total_details': total_details
        }
        
    finally:
        db.close()

def populate_redis_queue():
    """Populate Redis queue với pending matches"""
    logger.info("🔄 Populating Redis queue...")
    
    db = SessionLocal()
    try:
        # Clear existing queue first
        redis_client.clear_queue()
        logger.info("Cleared existing queue")
        
        # Get pending matches (prioritize recent ones)
        pending_matches = db.query(Match).filter(
            Match.details_crawled == False
        ).order_by(Match.created_at.desc()).limit(2000).all()
        
        if not pending_matches:
            logger.info("No pending matches found")
            return 0
            
        # Categorize matches by priority
        high_priority = []  # Recent matches (last 7 days)
        medium_priority = []  # Older matches

        from datetime import timezone
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=7)

        for match in pending_matches:
            try:
                if match.created_at and match.created_at >= cutoff_date:
                    high_priority.append(match.match_id)
                else:
                    medium_priority.append(match.match_id)
            except TypeError:
                # Handle timezone comparison issues
                medium_priority.append(match.match_id)
        
        # Add to queue with different priorities
        total_added = 0
        
        if high_priority:
            redis_client.add_matches_batch_to_queue(high_priority, priority=3)
            total_added += len(high_priority)
            logger.info(f"Added {len(high_priority)} high-priority matches (recent)")
            
        if medium_priority:
            redis_client.add_matches_batch_to_queue(medium_priority, priority=1)
            total_added += len(medium_priority)
            logger.info(f"Added {len(medium_priority)} medium-priority matches (older)")
        
        logger.info(f"✅ Total {total_added} matches added to queue")
        return total_added
        
    finally:
        db.close()

def test_crawl_sample():
    """Test crawl một vài matches để verify luồng hoạt động"""
    logger.info("🧪 Testing crawl flow with sample matches...")
    
    # Establish session
    if not crawler.establish_session():
        logger.error("Failed to establish crawler session")
        return False
        
    db = SessionLocal()
    try:
        # Get a few matches from queue
        sample_match_ids = redis_client.pop_matches_from_queue(batch_size=3)
        if not sample_match_ids:
            logger.warning("No matches in queue for testing")
            return False
            
        logger.info(f"Testing with {len(sample_match_ids)} sample matches")
        
        success_count = 0
        for i, match_id in enumerate(sample_match_ids, 1):
            try:
                logger.info(f"[{i}/{len(sample_match_ids)}] Testing match {match_id}...")
                
                success = crawler.crawl_match_details(db, match_id)
                if success:
                    success_count += 1
                    logger.info(f"[{i}/{len(sample_match_ids)}] ✅ {match_id}: Success")
                else:
                    logger.warning(f"[{i}/{len(sample_match_ids)}] ❌ {match_id}: Failed")
                    
            except Exception as e:
                logger.error(f"[{i}/{len(sample_match_ids)}] ❌ {match_id}: Error - {e}")
        
        logger.info(f"Test completed: {success_count}/{len(sample_match_ids)} successful")
        return success_count > 0
        
    finally:
        db.close()

def fix_match_details_flow():
    """Main function để fix toàn bộ luồng match_details"""
    logger.info("🔧 Starting match_details flow fix...")
    
    try:
        # 1. Analyze current state
        state = analyze_current_state()
        
        # 2. Populate Redis queue if needed
        if state['queue_length'] == 0 and state['pending_matches'] > 0:
            populate_redis_queue()
        else:
            logger.info(f"Queue already has {state['queue_length']} matches, skipping population")
        
        # 3. Test crawl flow
        if redis_client.get_queue_length() > 0:
            test_success = test_crawl_sample()
            if test_success:
                logger.info("✅ Test crawl successful - flow is working!")
            else:
                logger.warning("⚠️ Test crawl failed - check crawler configuration")
        
        # 4. Final state check
        print("\n" + "="*60)
        analyze_current_state()
        
        # 5. Recommendations
        print(f"\n💡 Next steps:")
        queue_length = redis_client.get_queue_length()
        if queue_length > 0:
            print(f"   1. Start background processor:")
            print(f"      python loop_crawl_match_details.py")
            print(f"   2. Or use Docker:")
            print(f"      docker-compose up -d match_details_loop")
            print(f"   3. Or use scheduler:")
            print(f"      python main.py --mode scheduler")
        else:
            print(f"   1. Check why queue is empty")
            print(f"   2. Verify matches exist in database")
            print(f"   3. Check crawler session establishment")
        
        logger.info("🎉 Match details flow fix completed!")
        
    except Exception as e:
        logger.error(f"❌ Error during fix: {e}", exc_info=True)

if __name__ == "__main__":
    fix_match_details_flow()
