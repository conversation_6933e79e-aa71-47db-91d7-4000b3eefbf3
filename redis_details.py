#!/usr/bin/env python3
"""
Script để xem chi tiết dữ liệu trong Redis
"""

import redis
import json
from config import settings

def show_redis_details():
    """Hi<PERSON>n thị chi tiết dữ liệu Redis"""
    try:
        r = redis.from_url(settings.REDIS_URL, decode_responses=True)
        
        print("🔍 **Redis Details**")
        print("=" * 60)
        
        # 1. Queue details
        queue_length = r.zcard("match_details_queue")
        print(f"📊 Queue: {queue_length} matches")
        
        if queue_length > 0:
            print("\n📋 All matches in queue:")
            matches = r.zrange("match_details_queue", 0, -1, withscores=True)
            for i, (match_id, score) in enumerate(matches, 1):
                print(f"  {i:3d}. {match_id} (priority: {score})")
        
        # 2. Match details
        match_details_keys = r.keys("match_details:*")
        print(f"\n📁 Match details: {len(match_details_keys)} matches")
        
        for key in match_details_keys:
            match_id = key.replace("match_details:", "")
            data = r.get(key)
            if data:
                try:
                    parsed_data = json.loads(data)
                    general = parsed_data.get('general', {})
                    finished = general.get('finished', False)
                    cancelled = general.get('cancelled', False)
                    started = general.get('started', False)
                    
                    print(f"\n  🏆 Match {match_id}:")
                    print(f"     - Finished: {finished}")
                    print(f"     - Cancelled: {cancelled}")
                    print(f"     - Started: {started}")
                    
                    # Show teams
                    home_team = general.get('homeTeam', {}).get('name', 'Unknown')
                    away_team = general.get('awayTeam', {}).get('name', 'Unknown')
                    print(f"     - Teams: {home_team} vs {away_team}")
                    
                    # Show match time
                    match_time = general.get('matchTimeUTC', 'Unknown')
                    print(f"     - Time: {match_time}")
                    
                except Exception as e:
                    print(f"  ❌ {match_id}: Error parsing data - {e}")
        
        print("\n" + "=" * 60)
        print("✅ Redis details completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    show_redis_details()
