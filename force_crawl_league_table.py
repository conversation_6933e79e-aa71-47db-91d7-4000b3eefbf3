#!/usr/bin/env python3
"""
Script để force crawl standings (bảng xếp hạng) cho các gi<PERSON>i đấu
"""

import sys
sys.path.append('/app')

from database import SessionLocal, League
from fotmob_crawler import crawler
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def force_crawl_league_tables():
    """Force crawl standings cho tất cả leagues trong DB"""
    db = SessionLocal()
    try:
        logger.info("Establishing session...")
        if not crawler.establish_session():
            logger.error("❌ Failed to establish session")
            return False

        leagues = db.query(League).all()
        if not leagues:
            logger.warning("⚠️ Không có league nào trong DB. Hãy chạy force_crawl_leagues.py trước.")
            return False

        success_count, fail_count = 0, 0
        for league in leagues:
            league_id = league.league_id
            logger.info(f"➡️ Crawling standings for league {league_id} - {league.name}")
            if crawler.crawl_league_table(db, league_id):
                success_count += 1
            else:
                fail_count += 1

        logger.info(f"✅ Done! Success={success_count}, Failed={fail_count}")
        return success_count > 0

    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    force_crawl_league_tables()
