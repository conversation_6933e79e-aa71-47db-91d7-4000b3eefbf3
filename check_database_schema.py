#!/usr/bin/env python3
"""
Script để kiểm tra database schema hiện tại
"""

import psycopg2
from config import settings
import json

def check_database_schema():
    """Kiểm tra schema và tables trong database"""
    try:
        # Parse DATABASE_URL
        db_url = settings.DATABASE_URL
        print(f"🔗 Connecting to: {db_url.split('@')[1] if '@' in db_url else 'database'}")
        
        # Connect to database
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        
        print("🔍 **Database Schema Check**")
        print("=" * 60)
        
        # 1. Check current schema
        cur.execute("SELECT current_schema();")
        current_schema = cur.fetchone()[0]
        print(f"📊 Current schema: {current_schema}")
        
        # 2. Check all schemas
        cur.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            ORDER BY schema_name;
        """)
        schemas = cur.fetchall()
        print(f"📁 Available schemas: {[s[0] for s in schemas]}")
        
        # 3. Check tables in public schema
        cur.execute("""
            SELECT table_name, table_type
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        tables = cur.fetchall()
        print(f"\n📋 Tables in 'public' schema: {len(tables)} tables")
        for table_name, table_type in tables:
            print(f"   - {table_name} ({table_type})")
        
        # 4. Check our specific tables
        our_tables = ['leagues', 'matches', 'match_details', 'crawl_logs']
        print(f"\n🎯 Checking our tables:")
        for table in our_tables:
            cur.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = %s;
            """, (table,))
            exists = cur.fetchone()[0] > 0
            
            if exists:
                # Get row count
                cur.execute(f"SELECT COUNT(*) FROM public.{table};")
                row_count = cur.fetchone()[0]
                print(f"   ✅ {table}: EXISTS ({row_count} rows)")
            else:
                print(f"   ❌ {table}: NOT EXISTS")
        
        # 5. Check table permissions
        print(f"\n🔐 Table permissions:")
        cur.execute("""
            SELECT grantee, table_name, privilege_type
            FROM information_schema.role_table_grants 
            WHERE table_schema = 'public' 
            AND table_name IN ('leagues', 'matches', 'match_details', 'crawl_logs')
            ORDER BY table_name, grantee;
        """)
        permissions = cur.fetchall()
        if permissions:
            for grantee, table_name, privilege in permissions:
                print(f"   - {grantee}: {privilege} on {table_name}")
        else:
            print("   - No specific permissions found (using default)")
        
        # 6. Check indexes
        print(f"\n📊 Indexes in public schema:")
        cur.execute("""
            SELECT tablename, indexname, indexdef
            FROM pg_indexes 
            WHERE schemaname = 'public' 
            AND tablename IN ('leagues', 'matches', 'match_details', 'crawl_logs')
            ORDER BY tablename, indexname;
        """)
        indexes = cur.fetchall()
        for tablename, indexname, indexdef in indexes:
            print(f"   - {tablename}.{indexname}")
        
        # 7. Test connection with SQLAlchemy
        print(f"\n🧪 Testing SQLAlchemy connection:")
        try:
            from database import engine, League
            with engine.connect() as conn:
                result = conn.execute("SELECT current_schema();")
                sqlalchemy_schema = result.fetchone()[0]
                print(f"   ✅ SQLAlchemy schema: {sqlalchemy_schema}")
                
                # Test table access
                result = conn.execute("SELECT COUNT(*) FROM leagues;")
                league_count = result.fetchone()[0]
                print(f"   ✅ Can access leagues table: {league_count} records")
                
        except Exception as e:
            print(f"   ❌ SQLAlchemy test failed: {e}")
        
        print("\n" + "=" * 60)
        print("✅ Database schema check completed!")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database_schema()
