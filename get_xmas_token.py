#!/usr/bin/env python3
"""
Script để lấy x-mas token từ request headers c<PERSON>a <PERSON>ot<PERSON>
"""

import requests
import re
import json
import os
import sys
sys.path.append('/app')

from database import SessionLocal, League
from fotmob_crawler import crawler
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_xmas_token_from_requests():
    """Lấy x-mas token từ request headers bằng Selenium"""
    driver = None
    try:
        # Cấu hình Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # Enable logging để capture network requests
        chrome_options.add_argument('--enable-logging')
        chrome_options.add_argument('--log-level=0')
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        logger.info("Khởi tạo Chrome driver...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # Truy cập trang FotMob
        logger.info("Truy cập trang FotMob...")
        driver.get('https://www.fotmob.com')
        
        # Đợi trang load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        logger.info("Trang đã load, đợi thêm để capture requests...")
        time.sleep(3)
        
        # Lấy performance logs để xem network requests
        logs = driver.get_log('performance')
        logger.info(f"Tìm thấy {len(logs)} performance logs")
        
        for log in logs:
            message = json.loads(log['message'])
            if message['message']['method'] == 'Network.requestWillBeSent':
                request_data = message['message']['params']
                url = request_data.get('request', {}).get('url', '')
                headers = request_data.get('request', {}).get('headers', {})
                
                # Kiểm tra nếu là request đến FotMob API
                if 'fotmob.com/api' in url:
                    logger.info(f"API Request: {url}")
                    logger.info(f"Request headers: {headers}")
                    
                    # Tìm x-mas token trong headers
                    for header_name, header_value in headers.items():
                        if 'x-mas' in header_name.lower():
                            logger.info(f"✅ Tìm thấy x-mas trong request header: {header_name} = {header_value}")
                            return header_value
                        
                        # Kiểm tra trong Authorization header
                        if header_name.lower() == 'authorization' and 'x-mas' in str(header_value).lower():
                            logger.info(f"✅ Tìm thấy x-mas trong Authorization: {header_value}")
                            # Extract token từ Authorization header
                            auth_match = re.search(r'x-mas[\s:=]+([^\s,;]+)', str(header_value), re.IGNORECASE)
                            if auth_match:
                                return auth_match.group(1)
                    
                    # Tìm trong request body nếu có
                    post_data = request_data.get('request', {}).get('postData')
                    if post_data and 'x-mas' in str(post_data).lower():
                        logger.info(f"Tìm thấy x-mas trong request body: {post_data}")
                        token_match = re.search(r'x-mas["\']?\s*[:=]\s*["\']?([^"\',\s}]+)', str(post_data), re.IGNORECASE)
                        if token_match:
                            token = token_match.group(1)
                            logger.info(f"✅ Tìm thấy x-mas token trong request body: {token[:20]}...")
                            return token
        
        # Thử trigger một API call bằng cách click vào elements
        logger.info("Thử trigger API calls bằng cách tương tác với trang...")
        
        try:
            # Tìm và click vào menu hoặc links có thể trigger API calls
            clickable_elements = driver.find_elements(By.CSS_SELECTOR, "a, button, [role='button']")
            logger.info(f"Tìm thấy {len(clickable_elements)} elements có thể click")
            
            for i, element in enumerate(clickable_elements[:5]):  # Chỉ thử 5 elements đầu
                try:
                    if element.is_displayed() and element.is_enabled():
                        logger.info(f"Click vào element {i+1}...")
                        driver.execute_script("arguments[0].click();", element)
                        time.sleep(1)
                        
                        # Kiểm tra logs mới
                        new_logs = driver.get_log('performance')
                        for log in new_logs[-10:]:  # Chỉ check 10 logs gần nhất
                            message = json.loads(log['message'])
                            if message['message']['method'] == 'Network.requestWillBeSent':
                                request_data = message['message']['params']
                                url = request_data.get('request', {}).get('url', '')
                                headers = request_data.get('request', {}).get('headers', {})
                                
                                if 'fotmob.com/api' in url:
                                    logger.info(f"New API Request: {url}")
                                    for header_name, header_value in headers.items():
                                        if 'x-mas' in header_name.lower():
                                            logger.info(f"✅ Tìm thấy x-mas token: {header_value}")
                                            return header_value
                except Exception as e:
                    logger.debug(f"Lỗi khi click element {i+1}: {e}")
                    continue
                    
        except Exception as e:
            logger.warning(f"Lỗi khi tương tác với trang: {e}")
        
        # Thử execute JavaScript để trigger API calls
        logger.info("Thử execute JavaScript để trigger API calls...")
        try:
            # Thử gọi các function có thể có trong window object
            js_code = """
            // Tìm các function có thể trigger API calls
            var functions = [];
            for (var prop in window) {
                if (typeof window[prop] === 'function' && prop.toLowerCase().includes('fetch')) {
                    functions.push(prop);
                }
            }
            return functions;
            """
            
            functions = driver.execute_script(js_code)
            logger.info(f"Tìm thấy các functions: {functions}")
            
            # Thử gọi fetch API trực tiếp
            fetch_code = """
            fetch('/api/leagues', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }).catch(e => console.log('Fetch error:', e));
            """
            
            driver.execute_script(fetch_code)
            time.sleep(2)
            
            # Kiểm tra logs sau khi execute JavaScript
            final_logs = driver.get_log('performance')
            for log in final_logs[-20:]:  # Check 20 logs gần nhất
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.requestWillBeSent':
                    request_data = message['message']['params']
                    url = request_data.get('request', {}).get('url', '')
                    headers = request_data.get('request', {}).get('headers', {})
                    
                    if 'api' in url:
                        logger.info(f"Final API Request: {url}")
                        logger.info(f"Final Request headers: {headers}")
                        
                        for header_name, header_value in headers.items():
                            if 'x-mas' in header_name.lower():
                                logger.info(f"✅ Tìm thấy x-mas token: {header_value}")
                                return header_value
                                
        except Exception as e:
            logger.warning(f"Lỗi khi execute JavaScript: {e}")
        
        logger.error("Không tìm thấy x-mas token trong request headers")
        return None
        
    except Exception as e:
        logger.error(f"Lỗi khi lấy x-mas token: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None
    finally:
        if driver:
            driver.quit()

def get_xmas_token_fallback():
    """Fallback method: Tìm token trong source code của trang"""
    try:
        logger.info("Sử dụng fallback method để tìm token...")
        
        session = requests.Session()
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        response = session.get('https://www.fotmob.com', headers=headers, timeout=30)
        if response.status_code != 200:
            logger.error(f"Không thể truy cập trang: {response.status_code}")
            return None
        
        html_content = response.text
        logger.info(f"HTML content length: {len(html_content)}")
        
        # Tìm trong JavaScript code
        patterns = [
            r'["\']x-mas["\']\s*:\s*["\']([^"\',]+)["\']',
            r'x-mas["\']?\s*[:=]\s*["\']([^"\',\s}]+)["\']?',
            r'headers\s*\[\s*["\']x-mas["\']\s*\]\s*=\s*["\']([^"\',]+)["\']',
            r'setRequestHeader\s*\(\s*["\']x-mas["\']\s*,\s*["\']([^"\',]+)["\']',
            r'"x-mas"\s*,\s*"([^"]+)"',
            r"'x-mas'\s*,\s*'([^']+)'"
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 10:  # Token hợp lệ thường dài
                    logger.info(f"✅ Tìm thấy x-mas token bằng pattern: {match[:20]}...")
                    return match
        
        # Tìm trong window object assignments
        window_patterns = [
            r'window\.[\w.]*x[_-]?mas[\w.]*\s*=\s*["\']([^"\',]+)["\']',
            r'window\.[\w.]*token[\w.]*\s*=\s*["\']([^"\',]+)["\']'
        ]
        
        for pattern in window_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 10:
                    logger.info(f"✅ Tìm thấy token trong window object: {match[:20]}...")
                    return match
        
        logger.error("Fallback method không tìm thấy token")
        return None
        
    except Exception as e:
        logger.error(f"Lỗi trong fallback method: {e}")
        return None

def update_crawler_token(x_mas_token):
    """Cập nhật x-mas token vào crawler và lưu vào file để sử dụng lại"""
    try:
        # Cập nhật token vào crawler
        crawler.working_headers['x-mas'] = x_mas_token
        crawler.session.headers.update(crawler.working_headers)
        
        logger.info("Đã cập nhật x-mas token vào crawler")
        
        # Lưu token vào file để sử dụng lại
        token_file = os.path.join(os.path.dirname(__file__), '.xmas_token')
        with open(token_file, 'w') as f:
            f.write(x_mas_token)
        
        logger.info(f"Đã lưu token vào file: {token_file}")
        return True
        
    except Exception as e:
        logger.error(f"Lỗi khi cập nhật token: {e}")
        return False

def crawl_leagues_with_token(x_mas_token):
    """Crawl leagues với x-mas token đã lấy được"""
    db = SessionLocal()
    try:
        # Cập nhật token vào crawler
        success = update_crawler_token(x_mas_token)
        if not success:
            return False
        
        # Clear existing leagues
        logger.info("Xóa leagues cũ...")
        db.query(League).delete()
        db.commit()
        
        # Crawl leagues
        logger.info("Bắt đầu crawl leagues...")
        success = crawler.crawl_all_leagues(db)
        
        if success:
            count = db.query(League).count()
            logger.info(f"✅ Crawl thành công {count} leagues")
        else:
            logger.error("❌ Crawl leagues thất bại")
            
        return success
        
    except Exception as e:
        logger.error(f"Lỗi khi crawl leagues: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Get and update x-mas token")
    parser.add_argument("--update-only", action="store_true", help="Only update token, don't crawl leagues")
    args = parser.parse_args()
    
    # Thử lấy x-mas token từ request headers
    token = get_xmas_token_from_requests()
    
    # Nếu không được, thử fallback method
    if not token:
        logger.info("Thử fallback method...")
        token = get_xmas_token_fallback()
    
    if token:
        logger.info(f"✅ Đã lấy được x-mas token: {token[:20]}...")
        
        if args.update_only:
            # Chỉ cập nhật token, không crawl leagues
            success = update_crawler_token(token)
            if success:
                logger.info("🎉 Đã cập nhật x-mas token thành công!")
                sys.exit(0)
            else:
                logger.error("💥 Cập nhật token thất bại")
                sys.exit(1)
        else:
            # Crawl leagues với token
            success = crawl_leagues_with_token(token)
            if success:
                logger.info("🎉 Hoàn thành crawl leagues!")
            else:
                logger.error("💥 Crawl leagues thất bại")
    else:
        logger.error("💥 Không lấy được x-mas token")
        sys.exit(1)