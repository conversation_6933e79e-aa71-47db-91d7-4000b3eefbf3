import schedule
import threading
import time
import random
import json
from sqlalchemy.orm import Session
from database import SessionLocal, MatchDetail
from fotmob_crawler import crawler
from redis_client import redis_client
from config import settings
import logging

logger = logging.getLogger(__name__)

class CrawlerScheduler:
    def __init__(self):
        self.running = False
        self.scheduler_thread = None
        self.match_details_thread = None
    
    def start(self):
        """Start the scheduler"""
        if self.running:
            return
        
        self.running = True
        
        # Schedule matches crawling every 6 hours
        schedule.every(6).hours.do(self.crawl_matches_job)
        
         # Schedule leagues crawling every 6 hours
        schedule.every(6).hours.do(self.crawl_leagues_job)
        
        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()
        
        # Start match details processing thread
        self.match_details_thread = threading.Thread(target=self._process_match_details)
        self.match_details_thread.daemon = True
        self.match_details_thread.start()
        
        logger.info("Scheduler started")
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        schedule.clear()
        logger.info("Scheduler stopped")
    
    def _run_scheduler(self):
        """Run scheduled jobs"""
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    def _process_match_details(self):
        """Process match details from Redis queue"""
        while self.running:
            try:
                match_id = redis_client.get_next_match_from_queue()
                if match_id:
                    db = SessionLocal()
                    try:
                        # Check if details already exist in PostgreSQL
                        existing_detail = db.query(MatchDetail).filter(
                            MatchDetail.match_id == match_id
                        ).first()
                        
                        if not existing_detail:
                            # Check if we have data in Redis
                            redis_data = redis_client.get_match_details(match_id)
                            if redis_data:
                                # Process existing Redis data
                                data = json.loads(redis_data)
                                is_finished = data.get('cancelled', False) or data.get('finished', False)
                                
                                if is_finished:
                                    # Move from Redis to PostgreSQL
                                    match_detail = MatchDetail(
                                        match_id=match_id,
                                        data=redis_data
                                    )
                                    db.merge(match_detail)
                                    
                                    # Update match record
                                    from database import Match
                                    match = db.query(Match).filter(Match.match_id == match_id).first()
                                    if match:
                                        match.details_crawled = True
                                    
                                    db.commit()
                                    logger.info(f"Moved match details for {match_id} from Redis to PostgreSQL (finished/cancelled)")
                                else:
                                    # Still ongoing, keep in Redis and re-queue for later processing
                                    redis_client.add_match_to_queue(match_id, redis_data, priority=2)
                                    logger.info(f"Re-queued ongoing match {match_id} for later processing")
                            else:
                                # No data in Redis, crawl fresh data
                                crawler.establish_session()
                                crawler.crawl_match_details(db, match_id)
                        else:
                            logger.info(f"Match details for {match_id} already exist in PostgreSQL, skipping")
                    
                    except Exception as e:
                        logger.error(f"Error processing match details for {match_id}: {e}")
                    finally:
                        db.close()
                    
                    time.sleep(random.uniform(1, 3))  # Random delay
                else:
                    # No matches in queue, wait longer
                    time.sleep(settings.MATCH_DETAILS_PROCESS_INTERVAL)
            
            except Exception as e:
                logger.error(f"Error in match details processing: {e}")
                time.sleep(60)
    
    def crawl_matches_job(self):
        """Job to crawl matches"""
        logger.info("Running scheduled matches crawl job...")
        db = SessionLocal()
        try:
            crawler.establish_session()
            crawler.crawl_matches_range(db, days_ahead=2)
        except Exception as e:
            logger.error(f"Error in matches crawl job: {e}")
        finally:
            db.close()
    
    def crawl_leagues_job(self):
        """Job to crawl leagues (chỉ chạy 1 lần)"""
        logger.info("Running scheduled leagues crawl job...")
        db = SessionLocal()
        try:
            crawler.establish_session()
            crawler.crawl_all_leagues(db)
        except Exception as e:
            logger.error(f"Error in leagues crawl job: {e}")
        finally:
            db.close()

# Create scheduler instance
scheduler = CrawlerScheduler()