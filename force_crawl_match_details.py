#!/usr/bin/env python3
"""
Daemon to crawl match_details in loop.
Reads MATCH_DETAILS_INTERVAL (seconds) from env (default 60).
"""

import os
import time
import logging
from database import SessionLocal, Match   
from fotmob_crawler import crawler
from redis_client import redis_client     

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def loop_crawl(interval: int = 30):
    logger.info("Starting match_details loop (interval=%s seconds)", interval)
    try:
        while True:
            db = SessionLocal()
            try:
                if not crawler.establish_session():
                    logger.error("Failed to establish session. Retry after sleep.")
                    time.sleep(max(5, interval))
                    continue

                # --- Ưu tiên lấy match_id từ Redis queue ---
                match_ids = redis_client.pop_matches_from_queue(batch_size=50)
                if match_ids:
                    logger.info("Popped %s match_ids from Redis queue", len(match_ids))
                else:
                    # Nếu Redis queue trống thì fallback sang DB
                    matches = db.query(Match).filter(Match.details_crawled == False).limit(50).all()
                    match_ids = [m.match_id for m in matches]
                    if not match_ids:
                        logger.info("No pending matches found in Redis or DB")

                # Crawl từng match_id
                for match_id in match_ids:
                    ok = crawler.crawl_match_details(db, match_id)
                    if ok:
                        logger.info(f"✅ Crawled {match_id}")
                    else:
                        logger.warning(f"❌ Failed {match_id}")

            except Exception as e:
                logger.exception("Error during crawl loop iteration: %s", e)
            finally:
                db.close()

            logger.info("Sleeping %s seconds before next iteration...", interval)
            time.sleep(interval)

    except KeyboardInterrupt:
        logger.info("Stopped by KeyboardInterrupt")

if __name__ == "__main__":
    interval_env = os.getenv("MATCH_DETAILS_INTERVAL", "60")
    try:
        interval = int(interval_env)
    except ValueError:
        interval = 60
    loop_crawl(interval=interval)
