import uvicorn
import argparse
import time
import logging
from database import create_tables, SessionLocal
from fotmob_crawler import crawler
from scheduler import scheduler
from config import settings

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_crawler_only():
    """Run crawler without API"""
    logger.info("Running crawler in standalone mode...")
    create_tables()
    
    db = SessionLocal()
    try:
        # Initialize session
        crawler.establish_session()
        
        # Crawl leagues (one-time)
        crawler.crawl_all_leagues(db)
        
        # Crawl matches for next 3 days
        crawler.crawl_matches_range(db, days_ahead=2)
        
        logger.info("Crawler completed successfully")
        
    except Exception as e:
        logger.error(f"Crawler failed: {e}")
    finally:
        db.close()

def run_scheduler_only():
    """Run scheduler without API"""
    logger.info("Running scheduler in standalone mode...")
    create_tables()
    scheduler.start()
    
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        logger.info("Stopping scheduler...")
        scheduler.stop()

def run_api_server():
    """Run FastAPI server"""
    logger.info("Starting FastAPI server...")
    uvicorn.run(
        "api:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.API_DEBUG
    )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="FotMob Crawler System")
    parser.add_argument(
        "--mode",
        choices=["api", "crawler", "scheduler"],
        default="api",
        help="Running mode: api (default), crawler, or scheduler"
    )
    
    args = parser.parse_args()
    
    if args.mode == "crawler":
        run_crawler_only()
    elif args.mode == "scheduler":
        run_scheduler_only()
    else:
        run_api_server()
