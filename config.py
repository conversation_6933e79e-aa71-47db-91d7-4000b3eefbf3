import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = "postgresql://user:password@localhost/fotmob_db"

    # Redis
    REDIS_URL: str = "redis://localhost:6379"

    # FotMob URLs
    FOTMOB_HOMEPAGE_URL: str = "https://www.fotmob.com"
    FOTMOB_BASE_URL: str = "https://www.fotmob.com/api/data"
    FOTMOB_CURRENCY_URL: str = "https://www.fotmob.com/api/currency"

    # Crawler settings
    CRAWLER_TIMEZONE: str = "Asia/Saigon"
    CRAWLER_COUNTRY_CODE: str = "VNM"
    CRAWLER_LOCALE: str = "en"

    # Proxy file
    PROXY_FILE: str = "proxy.txt"

    # API settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_DEBUG: bool = True

    # Cronjob settings
    MATCHES_CRAWL_SCHEDULE: str = "0 */6 * * *"  # Every 6 hours
    MATCH_DETAILS_PROCESS_INTERVAL: int = 60  # 60 seconds

    class Config:
        env_file = ".env"

settings = Settings()
