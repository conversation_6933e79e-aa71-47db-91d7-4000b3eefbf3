version: "3.8"

services:
  app:
    build: .
    container_name: live-crawler-app
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=**************************************************/fotmob_db
    volumes:
      - ./proxy.txt:/app/proxy.txt:ro
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  match_details_loop:
    build: .
    container_name: live-crawler-match-details
    command: python loop_crawl_match_details.py
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=**************************************************/fotmob_db
    volumes:
      - ./proxy.txt:/app/proxy.txt:ro
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    container_name: live-crawler-postgres
    environment:
      POSTGRES_DB: fotmob_db
      POSTGRES_USER: fotmob_user
      POSTGRES_PASSWORD: fotmob_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fotmob_user -d fotmob_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: live-crawler-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: live-crawler-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  pgadmin_data:
  redis_data:
