version: "3.8"

services:
  app:
    build: .
    container_name: live-crawler-app
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./proxy.txt:/app/proxy.txt:ro
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

  match_details_loop:
    build: .
    container_name: live-crawler-match-details
    command: python loop_crawl_match_details.py
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./proxy.txt:/app/proxy.txt:ro
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: live-crawler-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  redis_data:
