#!/usr/bin/env python3
"""
Script để kiểm tra tất cả Redis databases
"""

import redis
from config import settings

def check_all_databases():
    """Kiểm tra tất cả Redis databases"""
    try:
        print("🔍 **Redis All Databases Check**")
        print("=" * 60)
        
        # Kết nối Redis
        r = redis.from_url(settings.REDIS_URL, decode_responses=True)
        
        # Kiểm tra tất cả databases (0-15)
        for db_num in range(16):
            try:
                # <PERSON>y<PERSON>n sang database khác
                r.select(db_num)
                
                # Kiểm tra có dữ liệu không
                keys = r.keys("*")
                key_count = len(keys)
                
                if key_count > 0:
                    print(f"📊 Database db{db_num}: {key_count} keys")
                    
                    # Hiển thị một số keys
                    for key in keys[:5]:  # Chỉ hiển thị 5 keys đầu
                        key_type = r.type(key)
                        ttl = r.ttl(key)
                        ttl_str = f"{ttl}s" if ttl > 0 else "no expiry" if ttl == -1 else "expired"
                        print(f"   - {key} ({key_type}) - TTL: {ttl_str}")
                    
                    if len(keys) > 5:
                        print(f"   ... and {len(keys) - 5} more keys")
                    
                    print()
                else:
                    print(f"📊 Database db{db_num}: Empty")
                    
            except Exception as e:
                print(f"❌ Error checking db{db_num}: {e}")
        
        # Quay lại db0
        r.select(0)
        
        print("=" * 60)
        print("✅ All databases check completed!")
        
    except Exception as e:
        print(f"❌ Error connecting to Redis: {e}")

if __name__ == "__main__":
    check_all_databases()
