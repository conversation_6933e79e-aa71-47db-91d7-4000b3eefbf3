# 🗄️ **FotMob Database Setup Guide**

## 📋 **Tổng quan**

<PERSON>ệ thống sử dụng PostgreSQL để lưu trữ dữ liệu với 4 bảng chính:
- `leagues` - Thông tin giải đấu
- `matches` - Thông tin trận đấu
- `match_details` - Chi tiết trận đấu (chỉ finished/cancelled)
- `crawl_logs` - Log quá trình crawl

## 🚀 **C<PERSON>ch sử dụng**

### **1. Tạo database mới:**
```sql
-- Kết nối PostgreSQL
psql -U postgres

-- Tạo database
CREATE DATABASE fotmob_db;

-- Kết nối đến database
\c fotmob_db;
```

### **2. Chạy file SQL:**

#### **Option A: File đầy đủ (khuyến nghị)**
```bash
psql -U postgres -d fotmob_db -f create_tables.sql
```

#### **Option B: File cơ bản**
```bash
psql -U postgres -d fotmob_db -f create_basic_tables.sql
```

### **3. Kiểm tra kết quả:**
```sql
-- Xem các bảng đã tạo
\dt

-- Xem cấu trúc bảng
\d leagues
\d matches
\d match_details
\d crawl_logs

-- Xem các view
\dv

-- Xem các index
\di
```

## 📊 **Cấu trúc bảng**

### **1. Bảng LEAGUES**
```sql
CREATE TABLE leagues (
    id SERIAL PRIMARY KEY,
    league_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    country_code VARCHAR(10),
    data TEXT, -- JSON data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### **2. Bảng MATCHES**
```sql
CREATE TABLE matches (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) UNIQUE NOT NULL,
    date VARCHAR(8) NOT NULL, -- Format: YYYYMMDD
    league_id VARCHAR(50) NOT NULL,
    home_team VARCHAR(255),
    away_team VARCHAR(255),
    status VARCHAR(100),
    data TEXT, -- JSON data
    details_crawled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### **3. Bảng MATCH_DETAILS**
```sql
CREATE TABLE match_details (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) UNIQUE NOT NULL,
    data TEXT NOT NULL, -- JSON data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### **4. Bảng CRAWL_LOGS**
```sql
CREATE TABLE crawl_logs (
    id SERIAL PRIMARY KEY,
    endpoint VARCHAR(100) NOT NULL,
    date VARCHAR(8), -- Format: YYYYMMDD
    status VARCHAR(20) NOT NULL, -- success, failed, running
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 🔍 **Views hữu ích**

### **1. Today's Matches**
```sql
SELECT * FROM today_matches;
```

### **2. Recent Crawl Logs**
```sql
SELECT * FROM recent_crawl_logs;
```

### **3. Match Statistics**
```sql
SELECT * FROM match_stats;
```

## 📈 **Indexes được tạo**

- `idx_leagues_league_id` - Tìm kiếm theo league_id
- `idx_leagues_country_code` - Tìm kiếm theo country_code
- `idx_matches_match_id` - Tìm kiếm theo match_id
- `idx_matches_date` - Tìm kiếm theo ngày
- `idx_matches_league_id` - Tìm kiếm theo league_id
- `idx_match_details_match_id` - Tìm kiếm theo match_id
- `idx_crawl_logs_endpoint` - Tìm kiếm theo endpoint
- `idx_crawl_logs_date` - Tìm kiếm theo ngày
- `idx_crawl_logs_status` - Tìm kiếm theo status

## 🔧 **Triggers**

- **Auto-update `updated_at`** - Tự động cập nhật thời gian khi có thay đổi dữ liệu

## 🗑️ **Xóa database (nếu cần)**

```sql
-- Xóa tất cả bảng
DROP TABLE IF EXISTS crawl_logs CASCADE;
DROP TABLE IF EXISTS match_details CASCADE;
DROP TABLE IF EXISTS matches CASCADE;
DROP TABLE IF EXISTS leagues CASCADE;

-- Xóa function
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Xóa database
DROP DATABASE fotmob_db;
```

## 🔐 **Permissions (nếu cần)**

```sql
-- Tạo user
CREATE USER fotmob_user WITH PASSWORD 'fotmob_password_2024';

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO fotmob_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO fotmob_user;
```

## ✅ **Kiểm tra hoạt động**

```sql
-- Kiểm tra bảng đã tạo
SELECT COUNT(*) FROM leagues;
SELECT COUNT(*) FROM matches;
SELECT COUNT(*) FROM match_details;
SELECT COUNT(*) FROM crawl_logs;

-- Kiểm tra sample data
SELECT * FROM leagues WHERE league_id = '47';

-- Kiểm tra view
SELECT * FROM today_matches;
```

## 🎯 **Kết quả mong đợi**

Sau khi chạy thành công, bạn sẽ có:
- ✅ 4 bảng chính
- ✅ 9 indexes để tối ưu performance
- ✅ 3 views hữu ích
- ✅ 3 triggers tự động
- ✅ 1 sample league (Premier League)
- ✅ Comments mô tả cho từng bảng/cột

Hệ thống database đã sẵn sàng để sử dụng! 🚀
