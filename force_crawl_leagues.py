#!/usr/bin/env python3
"""
Script để force crawl leagues data
"""

import sys
sys.path.append('/app')

from database import SessionLocal, League
from fotmob_crawler import crawler
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def force_crawl_leagues():
    """Force crawl leagues without checking existing data"""
    db = SessionLocal()
    try:
        # Clear existing leagues if any
        logger.info("Clearing existing leagues...")
        db.query(League).delete()
        db.commit()
        
        # Establish session first
        logger.info("Establishing session...")
        if not crawler.establish_session():
            logger.error("Failed to establish session")
            return False
            
        # Force crawl leagues
        logger.info("Force crawling leagues...")
        success = crawler.crawl_all_leagues(db)
        
        if success:
            count = db.query(League).count()
            logger.info(f"Successfully crawled {count} leagues")
        else:
            logger.error("Failed to crawl leagues")
            
        return success
        
    except Exception as e:
        logger.error(f"Error: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    force_crawl_leagues()