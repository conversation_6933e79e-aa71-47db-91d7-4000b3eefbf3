from fastapi import FastAPI, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import Optional, List
import json
import logging
from database import get_db, create_tables, League, Match, MatchDetail, CrawlLog
from fotmob_crawler import crawler
from redis_client import redis_client
from scheduler import scheduler

logger = logging.getLogger(__name__)

app = FastAPI(
    title="FotMob Crawler API",
    description="API cho hệ thống crawler dữ li<PERSON>u bóng đá từ FotMob",
    version="1.0.0"
)

@app.on_event("startup")
async def startup_event():
    """Initialize database and start scheduler"""
    create_tables()
    scheduler.start()
    logger.info("Application started successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Stop scheduler"""
    scheduler.stop()
    logger.info("Application stopped")

# API Endpoints
@app.get("/")
async def root():
    return {"message": "FotMob Crawler API is running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    queue_length = redis_client.get_queue_length()
    return {
        "status": "healthy",
        "scheduler_running": scheduler.running,
        "redis_queue_length": queue_length
    }

@app.get("/leagues")
async def get_leagues(db: Session = Depends(get_db)):
    """Get all leagues"""
    leagues = db.query(League).all()
    return {
        "total": len(leagues),
        "leagues": [
            {
                "id": league.id,
                "league_id": league.league_id,
                "name": league.name,
                "country_code": league.country_code,
                "created_at": league.created_at,
                "updated_at": league.updated_at
            }
            for league in leagues
        ]
    }

@app.get("/leagues/{league_id}")
async def get_league_detail(league_id: str, db: Session = Depends(get_db)):
    """Get specific league details"""
    league = db.query(League).filter(League.league_id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail="League not found")
    
    return {
        "league_id": league.league_id,
        "name": league.name,
        "country_code": league.country_code,
        "data": json.loads(league.data) if league.data else {},
        "created_at": league.created_at,
        "updated_at": league.updated_at
    }

@app.get("/matches")
async def get_matches(
    date: Optional[str] = Query(None, description="Date in YYYYMMDD format"),
    league_id: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0),
    db: Session = Depends(get_db)
):
    """Get matches with filters"""
    query = db.query(Match)
    
    if date:
        query = query.filter(Match.date == date)
    if league_id:
        query = query.filter(Match.league_id == league_id)
    
    total = query.count()
    matches = query.order_by(desc(Match.created_at)).offset(offset).limit(limit).all()
    
    return {
        "total": total,
        "limit": limit,
        "offset": offset,
        "matches": [
            {
                "id": match.id,
                "match_id": match.match_id,
                "date": match.date,
                "league_id": match.league_id,
                "home_team": match.home_team,
                "away_team": match.away_team,
                "time"     : (json.loads(match.data).get("time") if match.data else None),                
                "details_crawled": match.details_crawled,
                "created_at": match.created_at,
                "updated_at": match.updated_at
            }
            for match in matches
        ]
    }
#xem trước các match_id trong queue.
@app.get("/queue/peek")
async def peek_queue(limit: int = Query(10, le=50)):
    queue_key = "match_details_queue"
    items = redis_client.redis.zrevrange(queue_key, 0, limit-1, withscores=True)
    return {"items": items}

@app.get("/matches/{match_id}")
async def get_match_detail(match_id: str, db: Session = Depends(get_db)):
    """Get specific match details"""
    match = db.query(Match).filter(Match.match_id == match_id).first()
    if not match:
        raise HTTPException(status_code=404, detail="Match not found")
    
    # Try to get details from PostgreSQL first
    match_detail = db.query(MatchDetail).filter(MatchDetail.match_id == match_id).first()
    details_data = None
    details_source = None
    
    if match_detail and match_detail.data:
        # Details found in PostgreSQL (finished/cancelled match)
        details_data = json.loads(match_detail.data)
        details_source = "postgresql"
    else:
        # Try to get details from Redis (ongoing match)
        redis_data = redis_client.get_match_details(match_id)
        if redis_data:
            details_data = json.loads(redis_data)
            details_source = "redis"
    
    return {
        "match": {
            "match_id": match.match_id,
            "date": match.date,
            "league_id": match.league_id,
            "home_team": match.home_team,
            "away_team": match.away_team,
            "status": match.status,
            "data": json.loads(match.data) if match.data else {},
            "details_crawled": match.details_crawled
        },
        "details": details_data,
        "details_source": details_source
    }

@app.post("/crawler/run-matches")
async def run_matches_crawler(
    background_tasks: BackgroundTasks,
    days_ahead: int = Query(2, description="Number of days ahead to crawl"),
    db: Session = Depends(get_db)
):
    """Manually trigger matches crawler"""
    def crawl_task():
        try:
            crawler.establish_session()
            crawler.crawl_matches_range(db, days_ahead)
        except Exception as e:
            logger.error(f"Manual matches crawl failed: {e}")
    
    background_tasks.add_task(crawl_task)
    return {"message": f"Matches crawler started for {days_ahead + 1} days", "status": "running"}

@app.post("/crawler/run-leagues")
async def run_leagues_crawler(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Manually trigger leagues crawler"""
    def crawl_task():
        try:
            crawler.establish_session()
            crawler.crawl_all_leagues(db)
        except Exception as e:
            logger.error(f"Manual leagues crawl failed: {e}")
    
    background_tasks.add_task(crawl_task)
    return {"message": "Leagues crawler started", "status": "running"}

@app.post("/crawler/process-match-details/{match_id}")
async def process_single_match_details(
    match_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Manually process match details for specific match"""
    # Check if match exists
    match = db.query(Match).filter(Match.match_id == match_id).first()
    if not match:
        raise HTTPException(status_code=404, detail="Match not found")
    
    def crawl_task():
        try:
            crawler.establish_session()
            crawler.crawl_match_details(db, match_id)
        except Exception as e:
            logger.error(f"Manual match details crawl failed for {match_id}: {e}")
    
    background_tasks.add_task(crawl_task)
    return {"message": f"Match details processing started for {match_id}", "status": "running"}

@app.get("/queue/status")
async def get_queue_status():
    """Get Redis queue status"""
    queue_length = redis_client.get_queue_length()
    return {
        "queue_length": queue_length,
        "status": "active" if queue_length > 0 else "empty"
    }

@app.post("/queue/clear")
async def clear_queue():
    """Clear Redis queue"""
    redis_client.clear_queue()
    return {"message": "Queue cleared", "status": "success"}

@app.get("/logs")
async def get_crawl_logs(
    endpoint: Optional[str] = Query(None),
    date: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0),
    db: Session = Depends(get_db)
):
    """Get crawl logs with filters"""
    query = db.query(CrawlLog)
    
    if endpoint:
        query = query.filter(CrawlLog.endpoint == endpoint)
    if date:
        query = query.filter(CrawlLog.date == date)
    if status:
        query = query.filter(CrawlLog.status == status)
    
    total = query.count()
    logs = query.order_by(desc(CrawlLog.created_at)).offset(offset).limit(limit).all()
    
    return {
        "total": total,
        "limit": limit,
        "offset": offset,
        "logs": [
            {
                "id": log.id,
                "endpoint": log.endpoint,
                "date": log.date,
                "status": log.status,
                "message": log.message,
                "created_at": log.created_at
            }
            for log in logs
        ]
    }

@app.get("/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get crawler statistics"""
    total_leagues = db.query(League).count()
    total_matches = db.query(Match).count()
    total_match_details = db.query(MatchDetail).count()
    matches_with_details = db.query(Match).filter(Match.details_crawled == True).count()
    queue_length = redis_client.get_queue_length()
    
    # Recent crawl logs
    recent_logs = db.query(CrawlLog).order_by(desc(CrawlLog.created_at)).limit(10).all()
    
    return {
        "stats": {
            "total_leagues": total_leagues,
            "total_matches": total_matches,
            "total_match_details": total_match_details,
            "matches_with_details": matches_with_details,
            "matches_without_details": total_matches - matches_with_details,
            "queue_length": queue_length,
            "scheduler_running": scheduler.running
        },
        "recent_logs": [
            {
                "endpoint": log.endpoint,
                "status": log.status,
                "message": log.message,
                "created_at": log.created_at
            }
            for log in recent_logs
        ]
    }
# Lấy bảng xếp hạng theo giải 
@app.get("/standings/{league_id}")
def get_standings(league_id: str, db: Session = Depends(get_db)):
    """
    Trả về bảng xếp hạng (standings) cho league_id
    - Nếu DB chưa có standings → tự động crawl lại từ API FotMob
    """
    league = db.query(League).filter(League.league_id == league_id).first()
    if not league:
        raise HTTPException(status_code=404, detail="League not found")

    # Nếu chưa có standings → crawl lại
    if not league.table_data or league.table_data.strip() == "":
        logger.info(f"⚠️ No standings in DB for league {league_id}, trying to crawl...")
        crawler.establish_session()
        success = crawler.crawl_league_table(db, league_id)

        if not success:
            raise HTTPException(status_code=404, detail="No standings data available for this league")

        # Lấy lại dữ liệu vừa crawl
        db.refresh(league)

    # Parse JSON standings
    try:
        return JSONResponse(content=json.loads(league.table_data))
    except Exception as e:
        logger.error(f"❌ Error parsing standings for league {league_id}: {e}")
        raise HTTPException(status_code=500, detail="Invalid standings data")

@app.get("/leagues/{league_id}/matches")
async def get_matches_by_league(
    league_id: str,
    limit: int = Query(100, le=1000),
    offset: int = Query(0),
    db: Session = Depends(get_db)
):
    """
    Trả về danh sách các trận đấu thuộc một giải (league_id)
    """
    query = db.query(Match).filter(Match.league_id == league_id)
    total = query.count()
    matches = query.order_by(desc(Match.date)).offset(offset).limit(limit).all()

    return {
        "league_id": league_id,
        "total": total,
        "limit": limit,
        "offset": offset,
        "matches": [
            {
                "id": match.id,
                "match_id": match.match_id,
                "date": match.date,
                "home_team": match.home_team,
                "away_team": match.away_team,
                "status": match.status,
                "time": (json.loads(match.data).get("time") if match.data else None),
                "details_crawled": match.details_crawled,
                "created_at": match.created_at,
                "updated_at": match.updated_at
            }
            for match in matches
        ]
    }
@app.get("/matches/upcoming")
async def get_upcoming_matches(
    league_id: Optional[str] = Query(None, description="Filter by league_id"),
    date: Optional[str] = Query(None, description="Filter by date in YYYYMMDD"),
    limit: int = Query(100, le=1000),
    offset: int = Query(0),
    db: Session = Depends(get_db)
):
    """
    Lấy danh sách các trận chưa diễn ra (not_started).
    Có thể filter theo league_id và date.
    """
    query = db.query(Match).filter(Match.status == "not_started")

    if league_id:
        query = query.filter(Match.league_id == league_id)
    if date:
        query = query.filter(Match.date == date)

    total = query.count()
    matches = (
        query.order_by(Match.date.asc())
        .offset(offset)
        .limit(limit)
        .all()
    )

    return {
        "total": total,
        "limit": limit,
        "offset": offset,
        "matches": [
            {
                "id": match.id,
                "match_id": match.match_id,
                "date": match.date,
                "league_id": match.league_id,
                "home_team": match.home_team,
                "away_team": match.away_team,
                "time": (json.loads(match.data).get("time") if match.data else None),
                "status": match.status,
                "created_at": match.created_at,
                "updated_at": match.updated_at,
            }
            for match in matches
        ],
    }
@app.get("/matches/live/{match_id}")
async def get_live_match_from_redis(match_id: str):
    """
    Lấy chi tiết trận trực tiếp (live) từ Redis cache.
    Trả 404 nếu không tìm thấy trong Redis.
    """
    redis_data = redis_client.get_match_details(match_id)
    if not redis_data:
        raise HTTPException(status_code=404, detail="Live details not found in Redis")
    try:
        return {"match_id": match_id, "details": json.loads(redis_data), "source": "redis"}
    except Exception:
        # nếu dữ liệu lưu là string non-json thì trả nguyên
        return {"match_id": match_id, "details_raw": redis_data, "source": "redis"}

# --- GET nhiều trận live / list live keys ---
@app.get("/matches/live")
async def list_live_matches(
    limit: int = Query(100, ge=1, le=1000, description="Max number of live match keys to list"),
    match_ids: Optional[str] = Query(None, description="Comma-separated match_ids to fetch (overrides listing)"),
):
    """
    Nếu `match_ids` truyền vào  -> trả details cho những id này.
    Nếu không -> liệt kê các live keys trong Redis (match_details:<id>) và trả list (max=limit).
    """
    if match_ids:
        ids = [s.strip() for s in match_ids.split(",") if s.strip()]
        result = redis_client.get_multiple_match_details(ids)
        # parse json where possible
        parsed = {}
        for mid, raw in result.items():
            if raw is None:
                parsed[mid] = None
            else:
                try:
                    parsed[mid] = json.loads(raw)
                except Exception:
                    parsed[mid] = raw
        return {"count": len(parsed), "matches": parsed, "source": "redis"}

    # else list keys
    keys = redis_client.list_live_match_ids(limit=limit)
    # convert keys 'match_details:123' -> ['123', ...]
    ids = [k.split(":", 1)[1] if ":" in k else k for k in keys]
    return {"count": len(ids), "match_ids": ids, "source": "redis"}