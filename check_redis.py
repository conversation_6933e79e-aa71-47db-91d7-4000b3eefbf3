#!/usr/bin/env python3
"""
Script để kiểm tra dữ liệu trong Redis
"""

import redis
import json
from config import settings

def check_redis_data():
    """Kiểm tra dữ liệu trong Redis"""
    try:
        # Kết nối Redis
        r = redis.from_url(settings.REDIS_URL, decode_responses=True)
        
        print("🔍 **Redis Data Check**")
        print("=" * 50)
        
        # 1. Kiểm tra queue
        queue_length = r.zcard("match_details_queue")
        print(f"📊 Queue length: {queue_length}")
        
        if queue_length > 0:
            # Lấy 5 match IDs đầu tiên
            matches = r.zrange("match_details_queue", 0, 4, withscores=True)
            print(f"📋 First 5 matches in queue:")
            for match_id, score in matches:
                print(f"  - {match_id} (priority: {score})")
        
        # 2. Kiểm tra match details
        match_details_keys = r.keys("match_details:*")
        print(f"📁 Match details in Redis: {len(match_details_keys)}")
        
        if match_details_keys:
            # Lấy 3 match details đầu tiên để xem
            for key in match_details_keys[:3]:
                match_id = key.replace("match_details:", "")
                data = r.get(key)
                if data:
                    try:
                        parsed_data = json.loads(data)
                        status = parsed_data.get('general', {}).get('finished', False)
                        cancelled = parsed_data.get('general', {}).get('cancelled', False)
                        print(f"  - {match_id}: finished={status}, cancelled={cancelled}")
                    except:
                        print(f"  - {match_id}: (invalid JSON)")
        
        # 3. Thống kê tổng quan
        total_keys = len(r.keys("*"))
        print(f"🗂️  Total keys in Redis: {total_keys}")
        
        # 4. Memory usage
        info = r.info('memory')
        used_memory = info.get('used_memory_human', 'N/A')
        print(f"💾 Memory used: {used_memory}")
        
        print("=" * 50)
        print("✅ Redis check completed!")
        
    except Exception as e:
        print(f"❌ Error connecting to Redis: {e}")

if __name__ == "__main__":
    check_redis_data()
