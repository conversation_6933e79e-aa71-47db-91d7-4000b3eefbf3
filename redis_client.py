import redis
import json
from typing import Dict, List, Optional
from config import settings

class RedisClient:
    def __init__(self):
        self.redis = redis.from_url(settings.REDIS_URL, decode_responses=True)

    # Thêm 1 match vào queue với priority
    def add_match_to_queue(self, match_id: str, match_data: str = None, priority: int = 1):
        queue_key = "match_details_queue"
        self.redis.zadd(queue_key, {match_id: priority})
        if match_data:
            self.set_match_details(match_id, match_data)

    # Thêm nhiều match 1 lượt
    def add_matches_batch_to_queue(self, match_ids: List[str], priority: int = 1):
        if not match_ids:
            return
        queue_key = "match_details_queue"
        with self.redis.pipeline() as pipe:
            for match_id in match_ids:
                pipe.zadd(queue_key, {match_id: priority})
            pipe.execute()

    # Pop nhiều match theo priority (cao nh<PERSON>t trướ<PERSON>)
    def pop_matches_from_queue(self, batch_size: int = 50) -> List[str]:
        queue_key = "match_details_queue"
        results = []

        try:
            # T<PERSON><PERSON> zpopmax trước (Redis 5.0+)
            try:
                # zpopmax với count parameter (Redis 6.2+)
                popped_items = self.redis.zpopmax(queue_key, count=batch_size)
                if popped_items:
                    # popped_items là list của tuples (member, score)
                    results = [item[0] for item in popped_items]
            except TypeError:
                # Fallback cho Redis 5.0-6.1 - pop từng item một
                for _ in range(batch_size):
                    result = self.redis.zpopmax(queue_key)
                    if result:
                        results.append(result[0][0])  # lấy match_id từ tuple (match_id, score)
                    else:
                        break
        except Exception:
            # Fallback cho Redis cũ hơn (< 5.0) - dùng zrange + zrem
            with self.redis.pipeline() as pipe:
                # Lấy items với score cao nhất
                items = self.redis.zrange(queue_key, -batch_size, -1, withscores=True)
                if items:
                    # Xóa các items đã lấy
                    members_to_remove = [item[0] for item in items]
                    pipe.zrem(queue_key, *members_to_remove)
                    pipe.execute()
                    results = members_to_remove

        return results
    # Pop 1 match
    def get_next_match_from_queue(self) -> Optional[str]:
        queue_key = "match_details_queue"

        try:
            # Thử zpopmax trước (Redis 5.0+)
            result = self.redis.zpopmax(queue_key)
            if result:
                # result là list của tuples [(member, score)]
                return result[0][0]  # lấy member từ tuple đầu tiên
        except Exception:
            # Fallback cho Redis cũ hơn (< 5.0) - dùng zrange + zrem
            items = self.redis.zrange(queue_key, -1, -1, withscores=True)
            if items:
                member = items[0][0]  # lấy member từ tuple (member, score)
                self.redis.zrem(queue_key, member)
                return member

        return None

    # Lấy số lượng trong queue
    def get_queue_length(self) -> int:
        return self.redis.zcard("match_details_queue")

    # Lưu chi tiết trận đấu 
    def set_match_details(self, match_id: str, data: str):
        key = f"match_details:{match_id}"
        try:
            details = json.loads(data) if isinstance(data, str) else data
            status = (
                details.get("general", {})
                       .get("status", {})
                       .get("type", "unknown")
            )

            # TTL theo status
            if status == "not_started":
                ttl = 24 * 3600      # 24h cho trận chưa diễn ra
            elif status == "ongoing":
                ttl = 3 * 3600       # 3h cho trận đang đá
            elif status in ["finished", "cancelled", "postponed"]:
                # không cần giữ Redis, đã lưu DB thì xóa
                self.delete_match_details(match_id)
                return
            else:
                ttl = 6 * 3600       # fallback cho status lạ

            self.redis.setex(key, ttl, json.dumps(details))
        except Exception:
            # fallback nếu parse fail → giữ 24h
            self.redis.setex(key, 86400, data)

    # Lấy chi tiết trận đấu từ cache
    def get_match_details(self, match_id: str) -> Optional[str]:
        key = f"match_details:{match_id}"
        return self.redis.get(key)

    # Xóa chi tiết trận đấu khỏi cache
    def delete_match_details(self, match_id: str):
        key = f"match_details:{match_id}"
        self.redis.delete(key)

    # Xóa toàn bộ queue
    def clear_queue(self):
        self.redis.delete("match_details_queue")

    def list_live_match_ids(self, pattern: str = "match_details:*", limit: int = 100) -> List[str]:
        """
        Trả về danh sách các match keys đang có trong Redis
        """
        try:
            # dùng scan để không chặn Redis
            cursor = 0
            keys = []
            while True:
                cursor, batch = self.redis.scan(cursor=cursor, match=pattern, count=100)
                keys.extend(batch)
                if cursor == 0 or len(keys) >= limit:
                    break
            return keys[:limit]
        except Exception:
            # fallback: trả rỗng nếu lỗi
            return []

    def get_multiple_match_details(self, match_ids: List[str]) -> Dict[str, Optional[str]]:
        """
        Lấy nhiều match details cùng lúc.
        match_ids: list các match_id
        Trả về dict {match_id: json_string_or_None}
        """
        keys = [f"match_details:{mid}" for mid in match_ids]
        try:
            values = self.redis.mget(keys)
            result = {}
            for mid, val in zip(match_ids, values):
                result[mid] = val  # val có thể là None
            return result
        except Exception:
            # fallback: từng phím
            result = {}
            for mid in match_ids:
                result[mid] = self.get_match_details(mid)
            return result
        
redis_client = RedisClient()
