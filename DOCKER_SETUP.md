# 🐳 Docker Setup Guide

## 📋 **C<PERSON>u hình hiện tại:**

### **Database**: 
- ✅ **Supabase PostgreSQL** (cloud) - như file `.env` hiện tại
- 🔗 Connection: `postgresql://postgres.kclyelbpamztfshgcsks:<EMAIL>:6543/postgres`

### **Redis**: 
- 🐳 **Docker Redis** container
- 🔗 Connection: `redis://redis:6379` (trong Docker network)

## 🚀 **Cách sử dụng:**

### **1. Chạy với Docker Compose:**
```bash
# Start Redis container
docker-compose up -d redis

# Start full application
docker-compose up -d

# View logs
docker-compose logs -f app
docker-compose logs -f match_details_loop
```

### **2. Chạy local development:**
```bash
# Start only Redis container
docker-compose up -d redis

# Run app locally (sẽ connect tới Supabase + Docker Redis)
python main.py --mode api
```

### **3. Stop services:**
```bash
# Stop all containers
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

## 📁 **Files cấu hình:**

### **`.env`** - Main environment file:
```env
# Database - Supabase (cloud)
DATABASE_URL=postgresql://postgres.kclyelbpamztfshgcsks:...

# Redis - sẽ được override trong Docker  
REDIS_URL=redis://localhost:6379
```

### **`.env.docker`** - Full Docker setup (nếu muốn dùng local PostgreSQL):
```env
# Database - Docker PostgreSQL
DATABASE_URL=**************************************************/fotmob_db

# Redis - Docker Redis
REDIS_URL=redis://redis:6379
```

## 🔧 **Docker Services:**

### **app** - Main API server:
- Port: `8000:8000`
- Environment: Sử dụng `.env` + override `REDIS_URL=redis://redis:6379`
- Depends on: Redis container

### **match_details_loop** - Background crawler:
- Command: `python loop_crawl_match_details.py`
- Environment: Sử dụng `.env` + override `REDIS_URL=redis://redis:6379`
- Depends on: Redis container

### **redis** - Redis cache:
- Image: `redis:7-alpine`
- Port: `6379:6379`
- Volume: `redis_data:/data`
- Health check: `redis-cli ping`

## 🎯 **Lợi ích của setup này:**

✅ **Database**: Dùng Supabase (managed, backup tự động, scalable)
✅ **Redis**: Dùng Docker (local, nhanh, dễ quản lý)
✅ **Flexibility**: Có thể chạy app local hoặc full Docker
✅ **Development**: Dễ debug và develop

## 🔍 **Kiểm tra:**

```bash
# Check Redis connection
docker-compose exec redis redis-cli ping

# Check app logs
docker-compose logs app

# Check Redis data
python check_redis.py
```

## 📝 **Notes:**

- **Database**: Vẫn lưu trên Supabase như hiện tại
- **Redis**: Chỉ dùng cho cache và queue, không cần backup
- **Environment**: File `.env` được override `REDIS_URL` trong Docker
- **Development**: Có thể chạy app local + Redis Docker để debug dễ hơn
