import requests
import json
import os
import time
import random
import subprocess
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from sqlalchemy.orm import Session
from config import settings
from database import League, Match, MatchDetail, CrawlLog
from redis_client import redis_client

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class FotMobCrawler:
    def __init__(self):
        required_settings = [
            settings.FOTMOB_BASE_URL,
            settings.FOTMOB_CURRENCY_URL,
            settings.CRAWLER_LOCALE,
            settings.CRAWLER_COUNTRY_CODE,
            settings.CRAWLER_TIMEZONE
        ]
        if not all(required_settings):
            raise ValueError("Missing required settings for FotMobCrawler")

        self.base_url = settings.FOTMOB_BASE_URL
        self.currency_url = settings.FOTMOB_CURRENCY_URL
        self.proxies = self.load_proxies()
        self.current_proxy_index = 0
        self.session = requests.Session()

        self.working_headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.fotmob.com/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Brave";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'sec-gpc': '1',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-mas': '',
            'Cookie': ''
        }

    # ------------------------- Proxy -------------------------

    def load_proxies(self) -> List[Dict[str, str]]:
        proxies = []
        try:
            if os.path.exists(settings.PROXY_FILE):
                with open(settings.PROXY_FILE, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and ':' in line:
                            parts = line.split(':')
                            if len(parts) >= 4:
                                ip, port, username, password = parts[0], parts[1], parts[2], parts[3]
                                proxy_url = f"http://{username}:{password}@{ip}:{port}"
                                proxies.append({'http': proxy_url, 'https': proxy_url})
                logger.info(f"Loaded {len(proxies)} proxies")
            else:
                logger.warning(f"Proxy file {settings.PROXY_FILE} not found, running without proxies")
        except Exception as e:
            logger.error(f"Error loading proxies: {e}")
        return proxies

    def get_next_proxy(self) -> Optional[Dict[str, str]]:
        if not self.proxies:
            return None
        proxy = self.proxies[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
        return proxy

    # ------------------------- Session / Token -------------------------

    def establish_session(self) -> bool:
        try:
            self.session = requests.Session()
            self.proxies = self.load_proxies()

            token_file = os.path.join(os.path.dirname(__file__), '.xmas_token')

            if os.path.exists(token_file):
                try:
                    with open(token_file, 'r') as f:
                        token = f.read().strip()
                    if token:
                        self.working_headers['x-mas'] = token
                        cookie_str = '; '.join([f"{k}={v}" for k, v in self.session.cookies.get_dict().items()])
                        if cookie_str:
                            self.working_headers['Cookie'] = cookie_str
                        self.session.headers.update(self.working_headers)
                        logger.info(f"✅ Loaded x-mas token from file: {token[:20]}...")
                        return True
                except Exception as e:
                    logger.warning(f"Failed to load token from file: {e}")

            logger.info("No x-mas token file found — attempting to refresh token via get_xmas_token.py")
            script_path = os.path.join(os.path.dirname(__file__), 'get_xmas_token.py')
            result = subprocess.run([sys.executable, script_path, '--update-only'],
                                    capture_output=True, text=True, timeout=120)
            if result.returncode != 0:
                logger.error(f"get_xmas_token.py failed ({result.returncode}). stderr: {result.stderr.strip()}")
                return False

            if os.path.exists(token_file):
                with open(token_file, 'r') as f:
                    token = f.read().strip()
                if token:
                    self.working_headers['x-mas'] = token
                    cookie_str = '; '.join([f"{k}={v}" for k, v in self.session.cookies.get_dict().items()])
                    if cookie_str:
                        self.working_headers['Cookie'] = cookie_str
                    self.session.headers.update(self.working_headers)
                    logger.info(f"✅ Refreshed and loaded x-mas token: {token[:20]}...")
                    return True
            logger.error("get_xmas_token.py completed but token file missing/empty")
            return False

        except Exception as e:
            logger.error(f"Failed to establish session: {e}")
            return False

    def refresh_xmas_token(self) -> bool:
        try:
            logger.info("Running get_xmas_token.py to refresh token...")
            script_path = os.path.join(os.path.dirname(__file__), 'get_xmas_token.py')
            result = subprocess.run([sys.executable, script_path, '--update-only'],
                                    capture_output=True, text=True, timeout=120)
            if result.returncode == 0:
                logger.info("get_xmas_token.py completed successfully")
                self.establish_session()
                return True
            logger.error(f"get_xmas_token.py failed: {result.returncode}, stderr={result.stderr}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("get_xmas_token.py timed out")
            return False
        except Exception as e:
            logger.error(f"Error running get_xmas_token.py: {e}")
            return False

    # ------------------------- Request Helper -------------------------

    def make_request(self, url: str, params: Dict = None, endpoint_name: str = "unknown", max_retries: int = 3) -> Optional[Dict]:
        for attempt in range(max_retries):
            try:
                proxy = self.get_next_proxy()
                try:
                    response = self.session.get(url, params=params, proxies=proxy, timeout=30)
                except requests.exceptions.ProxyError:
                    logger.warning(f"Proxy failed for {endpoint_name}, retrying without proxy...")
                    response = self.session.get(url, params=params, timeout=30)

                logger.info(f"{endpoint_name} - status: {response.status_code}")

                if response.status_code == 200:
                    try:
                        return response.json()
                    except json.JSONDecodeError:
                        logger.warning(f"{endpoint_name} - JSON decode error")
                        return None
                elif response.status_code == 401:
                    logger.warning(f"{endpoint_name} - Unauthorized, refreshing x-mas token...")
                    if attempt < max_retries - 1:
                        if not self.refresh_xmas_token():
                            self.establish_session()
                        time.sleep(random.uniform(2, 5))
                        continue
                elif response.status_code == 429:
                    logger.warning(f"{endpoint_name} - Rate limit exceeded, sleeping...")
                    time.sleep(random.uniform(10, 20))
                    continue
            except requests.exceptions.RequestException as e:
                logger.warning(f"{endpoint_name} - Attempt {attempt+1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(2, 5))
        logger.error(f"{endpoint_name} - All {max_retries} attempts failed")
        return None

    # ------------------------- Helper for matchDetails -------------------------

    def get_match_details(self, match_id: str) -> Optional[Dict]:
        """Fetch matchDetails từ API"""
        url = f"{self.base_url}/matchDetails"
        params = {"matchId": match_id}
        return self.make_request(url, params, "match_details")

    def save_match_details(self, db: Session, match_id: str, details: Dict):
        """Persist match details xuống PostgreSQL"""
        match_detail = db.query(MatchDetail).filter(MatchDetail.match_id == match_id).first()
        if not match_detail:
            match_detail = MatchDetail(match_id=match_id, data=json.dumps(details))
            db.add(match_detail)
        else:
            match_detail.data = json.dumps(details)

        match = db.query(Match).filter(Match.match_id == match_id).first()
        if match:
            match.details_crawled = True

        db.commit()
    
    def log_crawl_result(self, db: Session, endpoint: str, date: str = None, status: str = "success", message: str = ""):
        """Log crawl result to database"""
        try:
            log_entry = CrawlLog(
                endpoint=endpoint,
                date=date,
                status=status,
                message=message
            )
            db.add(log_entry)
            db.commit()
        except Exception as e:
            logger.error(f"Error logging crawl result: {e}")
            db.rollback()
    
    def crawl_all_leagues(self, db: Session) -> bool:
        """Crawl all leagues data (chỉ crawl 1 lần)"""
        existing_leagues = db.query(League).count()
        if existing_leagues > 0:
            logger.info("Leagues already exist in database, skipping...")
            return True
        
        logger.info("Crawling all leagues...")
        url = f"{self.base_url}/allLeagues"
        params = {
            'locale': settings.CRAWLER_LOCALE,
            'country': settings.CRAWLER_COUNTRY_CODE
        }
        
        data = self.make_request(url, params, "all_leagues")
        if not data or 'countries' not in data:
            self.log_crawl_result(db, "all_leagues", status="failed", message="Invalid or empty data")
            return False
        
        try:
            leagues_count = 0
            for country in data['countries']:
                country_code = country.get('ccode', '')
                if 'leagues' not in country:
                    continue
                for league_data in country['leagues']:
                    league = League(
                        league_id=str(league_data.get('id', '')),
                        name=league_data.get('name', ''),
                        country_code=country_code,
                        data=json.dumps(league_data)
                    )
                    db.merge(league)
                    leagues_count += 1
            
            db.commit()
            logger.info(f"Saved {leagues_count} leagues to database")
            self.log_crawl_result(db, "all_leagues", status="success", message=f"Crawled {leagues_count} leagues")
            return True
        except Exception as e:
            logger.error(f"Error saving leagues: {e}")
            self.log_crawl_result(db, "all_leagues", status="failed", message=str(e))
            db.rollback()
            return False
    
    def crawl_matches_for_date(self, db: Session, date: str) -> bool:
        """Crawl matches for specific date"""
        logger.info(f"Crawling matches for date: {date}")
        url = f"{self.base_url}/matches"
        params = {
            'date': date,
            'timezone': settings.CRAWLER_TIMEZONE,
            'ccode3': settings.CRAWLER_COUNTRY_CODE
        }
        
        data = self.make_request(url, params, f"matches_{date}")
        if not data or 'leagues' not in data:
            self.log_crawl_result(db, "matches", date, "failed", message="Invalid or empty data")
            return False
        
        try:
            match_ids_to_queue = []
            matches_count = 0
            
            for league in data['leagues']:
                league_id = str(league.get('id', ''))
                if 'matches' not in league:
                    continue
                for match_data in league['matches']:
                    match_id = str(match_data.get('id', ''))
                    match = Match(
                        match_id=match_id,
                        date=date,
                        league_id=league_id,
                        home_team=match_data.get('home', {}).get('name', ''),
                        away_team=match_data.get('away', {}).get('name', ''),
                        status=match_data.get('status', {}).get('reason', {}).get('short', ''),
                        data=json.dumps(match_data)
                    )
                    db.merge(match)
                    match_ids_to_queue.append(match_id)
                    matches_count += 1
            
            db.commit()
            
            if match_ids_to_queue:
                redis_client.add_matches_batch_to_queue(match_ids_to_queue)
                logger.info(f"Added {len(match_ids_to_queue)} match IDs to Redis queue")
            
            logger.info(f"Saved {matches_count} matches for date {date}")
            self.log_crawl_result(db, "matches", date, "success", message=f"Crawled {matches_count} matches")
            return True
        except Exception as e:
            logger.error(f"Error saving matches for {date}: {e}")
            self.log_crawl_result(db, "matches", date, "failed", message=str(e))
            db.rollback()
            return False
    
    def crawl_match_details(self, db: Session, match_id: str) -> bool:
        """Crawl match details từ FotMob, lưu Redis (live), và persist DB khi match kết thúc"""
        try:
            url = f"{self.base_url}/matchDetails"
            params = {"matchId": match_id}
            response = self.session.get(url, params=params, timeout=10)

            if response.status_code != 200:
                logger.warning(f"Failed to fetch match details for {match_id}, status={response.status_code}")
                return False

            data = response.json()
            if not data:
                logger.error(f"No data returned for match {match_id}")
                return False

            # --- Lưu vào Redis ---
            redis_client.set_match_details(match_id, json.dumps(data))
            logger.info(f"✅ Saved match details for {match_id} into Redis")

            # --- Kiểm tra status ---
            general = data.get("general", {})
            status_type = general.get("status", {}).get("type")
            is_finished = general.get("finished", False)
            is_cancelled = general.get("cancelled", False)

            # Check both status.type and general.finished/cancelled
            should_save_to_db = (
                status_type in ["finished", "postponed", "cancelled"] or
                is_finished or is_cancelled
            )

            if should_save_to_db:
                try:
                    match_detail = db.query(MatchDetail).filter(MatchDetail.match_id == match_id).first()
                    if not match_detail:
                        match_detail = MatchDetail(match_id=match_id, data=json.dumps(data))
                        db.add(match_detail)
                    else:
                        match_detail.data = json.dumps(data)

                    # Update cờ `details_crawled` ở bảng Match
                    match = db.query(Match).filter(Match.match_id == match_id).first()
                    if match:
                        match.details_crawled = True

                    db.commit()
                    logger.info(f"💾 Persisted match {match_id} to PostgreSQL (finished={is_finished}, cancelled={is_cancelled}, status_type={status_type})")

                    # --- Xóa khỏi Redis ---
                    redis_client.delete_match_details(match_id)
                    logger.info(f"🗑️ Removed match {match_id} from Redis after persisting to DB")

                except Exception as e:
                    db.rollback()
                    logger.error(f"DB error while saving match {match_id}: {e}", exc_info=True)
                    return False

            return True

        except Exception as e:
            logger.error(f"Error crawling match details {match_id}: {e}", exc_info=True)
            return False

    def crawl_all_matches(self, db: Session, days_back: int = 3, days_forward: int = 7) -> bool:
        success_count = 0
        today = datetime.now()
        total_days = days_back + days_forward + 1

        for i in range(-days_back, days_forward + 1):
            target_date = today + timedelta(days=i)
            date_str = target_date.strftime('%Y%m%d')

            logger.info(f"===> Crawling matches for {date_str}")
            if self.crawl_matches_for_date(db, date_str):
                success_count += 1

            # Sleep nhẹ để tránh bị rate limit
            time.sleep(random.uniform(1, 3))

        logger.info(f"✅ Successfully crawled {success_count}/{total_days} days of matches")
        self.log_crawl_result(
            db,
            "all_matches",
            status="success" if success_count > 0 else "failed",
            message=f"Crawled {success_count}/{total_days} days"
        )
        return success_count > 0

    def crawl_matches_range(self, db: Session, days_ahead: int = 2) -> bool:
        """Crawl matches from today to future days"""
        success_count = 0
        today = datetime.now()
        
        for i in range(days_ahead + 1):
            target_date = today + timedelta(days=i)
            date_str = target_date.strftime('%Y%m%d')
            
            if self.crawl_matches_for_date(db, date_str):
                success_count += 1
            
            if i < days_ahead:
                time.sleep(random.uniform(1, 3))
        
        logger.info(f"Successfully crawled {success_count}/{days_ahead + 1} days")
        self.log_crawl_result(db, "matches_range", status="success" if success_count > 0 else "failed",
                            message=f"Crawled {success_count}/{days_ahead + 1} days")
        return success_count > 0
    

    def get_match_details(self, match_id: str) -> Optional[Dict]:
        url = f"{self.base_url}/matchDetails"
        params = {"matchId": match_id}
        return self.make_request(url, params, "match_details")

    def save_match_details(self, db: Session, match_id: str, details: Dict):
        match_detail = db.query(MatchDetail).filter(MatchDetail.match_id == match_id).first()
        if not match_detail:
            match_detail = MatchDetail(match_id=match_id, data=json.dumps(details))
            db.add(match_detail)
        else:
            match_detail.data = json.dumps(details)

        match = db.query(Match).filter(Match.match_id == match_id).first()
        if match:
            match.details_crawled = True

        db.commit()

    def crawl_all_match_details(self, db: Session, only_not_crawled: bool = True, batch_size: int = 100) -> bool:
        try:
            logger.info("===> Starting crawl_all_match_details...")
            query = db.query(Match.match_id)
            if only_not_crawled:
                query = query.filter(Match.details_crawled == False)

            match_ids = [row.match_id for row in query.all()]
            if not match_ids:
                logger.info("No matches found to crawl details.")
                return True

            success_count, fail_count = 0, 0
            for i, match_id in enumerate(match_ids, start=1):
                try:
                    details = self.get_match_details(match_id)
                    if not details:
                        fail_count += 1
                        continue

                    status = details.get("general", {}).get("status", {}).get("type")

                    if status in ["finished", "cancelled", "postponed"]:
                        # Trận đã xong → lưu DB, xóa Redis
                        self.save_match_details(db, match_id, details)
                        redis_client.delete_match_details(match_id)
                        logger.info(f"Saved match {match_id} details to PostgreSQL (status={status}), removed from Redis")
                    else:
                        # Trận chưa đá hoặc đang diễn ra (hoặc status lạ) → để Redis quyết TTL
                        redis_client.set_match_details(match_id, details)
                        logger.info(f"Cached match {match_id} details to Redis (status={status})")

                    success_count += 1

                except Exception as e:
                    fail_count += 1
                    logger.error(f"Failed crawling match {match_id}: {e}")

                # Sleep tránh bị block
                time.sleep(random.uniform(1, 2))
                if i % batch_size == 0:
                    logger.info(f"Processed {i}/{len(match_ids)} matches so far...")

            logger.info(f"✅ Finished crawling match_details: {success_count} success, {fail_count} failed")
            return success_count > 0

        except Exception as e:
            logger.error(f"Error in crawl_all_match_details: {e}")
            return False

        # bảng xếp hạng 
    def crawl_league_table(self, db: Session, league_id: str) -> bool:
        """Crawl bảng xếp hạng (standings) cho một giải đấu"""
        try:
            url = f"{self.base_url}/tltable"
            params = {"leagueId": league_id}

            data = self.make_request(url, params, f"league_table_{league_id}")
            if not data:
                logger.warning(f"No standings data for league {league_id}")
                self.log_crawl_result(
                    db, f"league_table_{league_id}", status="failed", message="Empty response"
                )
                return False

            # Xác định standings data
            standings_data = None
            if isinstance(data, list):
                standings_data = data
                logger.info(f"✅ Found standings list for league {league_id}")
            elif "table" in data:
                standings_data = data["table"]
                logger.info(f"✅ Found 'table' standings for league {league_id}")
            elif "groups" in data:
                standings_data = data["groups"]
                logger.info(f"✅ Found 'groups' standings for league {league_id}")
            elif "stages" in data:
                standings_data = data["stages"]
                logger.info(f"✅ Found 'stages' standings for league {league_id}")

            if not standings_data:
                if isinstance(data, dict):
                    logger.warning(f"No recognizable standings for league {league_id}, keys={list(data.keys())}")
                elif isinstance(data, list):
                    logger.warning(f"No recognizable standings for league {league_id}, got a list with length {len(data)}")
                else:
                    logger.warning(f"No recognizable standings for league {league_id}, type={type(data)}")
                self.log_crawl_result(
                    db, f"league_table_{league_id}", status="failed", message="No standings keys"
                )
                return False

            # Lưu vào DB
            league = db.query(League).filter(League.league_id == str(league_id)).first()
            if league:
                league.table_data = json.dumps(standings_data)
                db.commit()
                logger.info(f"💾 Saved standings for league_id={league_id}")
                self.log_crawl_result(db, f"league_table_{league_id}", status="success")
                return True
            else:
                logger.warning(f"League {league_id} not found in DB")
                self.log_crawl_result(
                    db, f"league_table_{league_id}", status="failed", message="League not found"
                )
                return False

        except Exception as e:
            logger.error(f"Error crawling league table for {league_id}: {e}", exc_info=True)
            self.log_crawl_result(db, f"league_table_{league_id}", status="failed", message=str(e))
            db.rollback()
            return False




crawler = FotMobCrawler()