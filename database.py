from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func
from datetime import datetime
import json
from config import settings

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class League(Base):
    __tablename__ = "leagues"
    
    id = Column(Integer, primary_key=True, index=True)
    league_id = Column(String, unique=True, index=True)
    name = Column(String, index=True)
    country_code = Column(String)
    data = Column(Text)  # JSON data
    table_data = Column(Text) 
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Match(Base):
    __tablename__ = "matches"
    
    id = Column(Integer, primary_key=True, index=True)
    match_id = Column(String, unique=True, index=True)
    date = Column(String, index=True)  # Format: YYYYMMDD
    league_id = Column(String, index=True)
    home_team = Column(String)
    away_team = Column(String)
    status = Column(String)
    data = Column(Text)  # JSON data
    details_crawled = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class MatchDetail(Base):
    __tablename__ = "match_details"
    
    id = Column(Integer, primary_key=True, index=True)
    match_id = Column(String, unique=True, index=True)
    data = Column(Text)  # JSON data
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class CrawlLog(Base):
    __tablename__ = "crawl_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    endpoint = Column(String, index=True)
    date = Column(String, index=True)  # For matches endpoint
    status = Column(String)  # success, failed
    message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    Base.metadata.create_all(bind=engine)
