version: "3.9"

services:
  match_details_loop:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: live-crawler-match-details
    command: ["python", "force_crawl_match_details.py"]
    env_file:
      - .env
    environment:
      - MATCH_DETAILS_INTERVAL=${MATCH_DETAILS_INTERVAL:-60}
      - PYTHONUNBUFFERED=1
    volumes:
      - ./:/app
    depends_on:
      - redis
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
