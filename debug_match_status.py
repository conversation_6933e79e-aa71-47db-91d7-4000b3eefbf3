#!/usr/bin/env python3
"""
Debug script để kiểm tra match status format
"""

from redis_client import redis_client
import json

def debug_match_status():
    """Debug match status format"""
    print("🔍 Debugging match status format...")
    
    # Get match details from Redis
    keys = redis_client.list_live_match_ids()
    if not keys:
        print("❌ No match details found in Redis")
        return
        
    print(f"📊 Found {len(keys)} match details in Redis")
    
    # Check first few matches
    for i, match_key in enumerate(keys[:3], 1):
        match_id = match_key.replace('match_details:', '')
        data = redis_client.get_match_details(match_id)
        
        if not data:
            print(f"[{i}] ❌ {match_id}: No data")
            continue
            
        try:
            parsed = json.loads(data)
            general = parsed.get('general', {})
            status = general.get('status', {})
            
            print(f"\n[{i}] 🏆 Match {match_id}:")
            print(f"    Status object: {status}")
            print(f"    Status type: {status.get('type')}")
            print(f"    Status utcTime: {status.get('utcTime')}")
            print(f"    Status started: {status.get('started')}")
            print(f"    Status finished: {status.get('finished')}")
            print(f"    General finished: {general.get('finished')}")
            print(f"    General cancelled: {general.get('cancelled')}")
            print(f"    General started: {general.get('started')}")
            
            # Check if this should be saved to DB
            status_type = status.get('type')
            should_save = status_type in ["finished", "postponed", "cancelled"]
            print(f"    Should save to DB: {should_save} (status='{status_type}')")
            
        except Exception as e:
            print(f"[{i}] ❌ {match_id}: Error parsing - {e}")

if __name__ == "__main__":
    debug_match_status()
