#!/usr/bin/env python3
"""
Script để force crawl matches data (từ hiện tại đến 3 tháng tới)
"""

import sys
sys.path.append('/app')

from database import SessionLocal, Match
from fotmob_crawler import crawler
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def force_crawl_matches():
    db = SessionLocal()
    try:
        # Clear matches table
        logger.info("Clearing existing matches...")
        db.query(Match).delete()
        db.commit()

        # Establish session
        logger.info("Establishing session...")
        if not crawler.establish_session():
            logger.error("Failed to establish session")
            return False

        # Crawl matches 
        logger.info("Force crawling matches...")
        success = crawler.crawl_all_matches(db, days_back=2, days_forward=5)

        if success:
            count = db.query(Match).count()
            logger.info(f"✅ Successfully crawled {count} matches")
        else:
            logger.error("❌ Failed to crawl matches")

        return success

    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    force_crawl_matches()
