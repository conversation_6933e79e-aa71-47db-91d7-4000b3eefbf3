-- =====================================================
-- FotMob Data Crawler - PostgreSQL Database Schema
-- =====================================================

-- Tạo database (nếu chưa có)
-- CREATE DATABASE fotmob_db;

-- Kết nối đến database
-- \c fotmob_db;

-- =====================================================
-- 1. BẢNG LEAGUES
-- =====================================================
CREATE TABLE IF NOT EXISTS leagues (
    id SERIAL PRIMARY KEY,
    league_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    country_code VARCHAR(10),
    data TEXT, -- JSON data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index cho leagues
CREATE INDEX IF NOT EXISTS idx_leagues_league_id ON leagues(league_id);
CREATE INDEX IF NOT EXISTS idx_leagues_country_code ON leagues(country_code);

-- =====================================================
-- 2. BẢNG MATCHES
-- =====================================================
CREATE TABLE IF NOT EXISTS matches (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) UNIQUE NOT NULL,
    date VARCHAR(8) NOT NULL, -- Format: YYYYMMDD
    league_id VARCHAR(50) NOT NULL,
    home_team VARCHAR(255),
    away_team VARCHAR(255),
    status VARCHAR(100),
    data TEXT, -- JSON data
    details_crawled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index cho matches
CREATE INDEX IF NOT EXISTS idx_matches_match_id ON matches(match_id);
CREATE INDEX IF NOT EXISTS idx_matches_date ON matches(date);
CREATE INDEX IF NOT EXISTS idx_matches_league_id ON matches(league_id);
CREATE INDEX IF NOT EXISTS idx_matches_status ON matches(status);

-- =====================================================
-- 3. BẢNG MATCH_DETAILS
-- =====================================================
CREATE TABLE IF NOT EXISTS match_details (
    id SERIAL PRIMARY KEY,
    match_id VARCHAR(50) UNIQUE NOT NULL,
    data TEXT NOT NULL, -- JSON data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index cho match_details
CREATE INDEX IF NOT EXISTS idx_match_details_match_id ON match_details(match_id);

-- =====================================================
-- 4. BẢNG CRAWL_LOGS
-- =====================================================
CREATE TABLE IF NOT EXISTS crawl_logs (
    id SERIAL PRIMARY KEY,
    endpoint VARCHAR(100) NOT NULL,
    date VARCHAR(8), -- Format: YYYYMMDD
    status VARCHAR(20) NOT NULL, -- success, failed, running
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index cho crawl_logs
CREATE INDEX IF NOT EXISTS idx_crawl_logs_endpoint ON crawl_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_crawl_logs_date ON crawl_logs(date);
CREATE INDEX IF NOT EXISTS idx_crawl_logs_status ON crawl_logs(status);
CREATE INDEX IF NOT EXISTS idx_crawl_logs_created_at ON crawl_logs(created_at);

-- =====================================================
-- 5. BẢNG TEAMS (nếu cần)
-- =====================================================
CREATE TABLE IF NOT EXISTS teams (
    id SERIAL PRIMARY KEY,
    team_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    country_code VARCHAR(10),
    data TEXT, -- JSON data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index cho teams
CREATE INDEX IF NOT EXISTS idx_teams_team_id ON teams(team_id);
CREATE INDEX IF NOT EXISTS idx_teams_name ON teams(name);

-- =====================================================
-- 6. BẢNG VENUES (nếu cần)
-- =====================================================
CREATE TABLE IF NOT EXISTS venues (
    id SERIAL PRIMARY KEY,
    venue_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    city VARCHAR(100),
    country_code VARCHAR(10),
    data TEXT, -- JSON data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index cho venues
CREATE INDEX IF NOT EXISTS idx_venues_venue_id ON venues(venue_id);
CREATE INDEX IF NOT EXISTS idx_venues_name ON venues(name);

-- =====================================================
-- 7. VIEWS HỮU ÍCH
-- =====================================================

-- View: Today's matches
CREATE OR REPLACE VIEW today_matches AS
SELECT 
    m.match_id,
    m.date,
    l.name as league_name,
    m.home_team,
    m.away_team,
    m.status,
    m.details_crawled
FROM matches m
JOIN leagues l ON m.league_id = l.league_id
WHERE m.date = TO_CHAR(CURRENT_DATE, 'YYYYMMDD')
ORDER BY m.created_at DESC;

-- View: Recent crawl logs
CREATE OR REPLACE VIEW recent_crawl_logs AS
SELECT 
    endpoint,
    date,
    status,
    message,
    created_at
FROM crawl_logs
ORDER BY created_at DESC
LIMIT 100;

-- View: Match statistics
CREATE OR REPLACE VIEW match_stats AS
SELECT 
    COUNT(*) as total_matches,
    COUNT(CASE WHEN details_crawled = TRUE THEN 1 END) as details_crawled,
    COUNT(CASE WHEN status = 'FT' THEN 1 END) as finished_matches,
    COUNT(CASE WHEN status = 'LIVE' THEN 1 END) as live_matches
FROM matches;

-- =====================================================
-- 8. TRIGGERS CHO UPDATED_AT
-- =====================================================

-- Function để update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers cho các bảng
CREATE TRIGGER update_leagues_updated_at 
    BEFORE UPDATE ON leagues 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_matches_updated_at 
    BEFORE UPDATE ON matches 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_match_details_updated_at 
    BEFORE UPDATE ON match_details 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at 
    BEFORE UPDATE ON teams 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_venues_updated_at 
    BEFORE UPDATE ON venues 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 9. SAMPLE DATA (nếu cần)
-- =====================================================

-- Insert sample league
INSERT INTO leagues (league_id, name, country_code, data) 
VALUES (
    '47', 
    'Premier League', 
    'ENG', 
    '{"id": 47, "name": "Premier League", "countryCode": "ENG"}'
) ON CONFLICT (league_id) DO NOTHING;

-- =====================================================
-- 10. GRANTS (nếu cần)
-- =====================================================

-- Grant permissions cho user
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO fotmob_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO fotmob_user;

-- =====================================================
-- 11. COMMENTS
-- =====================================================

COMMENT ON TABLE leagues IS 'Bảng lưu trữ thông tin các giải đấu';
COMMENT ON TABLE matches IS 'Bảng lưu trữ thông tin các trận đấu';
COMMENT ON TABLE match_details IS 'Bảng lưu trữ chi tiết trận đấu (chỉ finished/cancelled)';
COMMENT ON TABLE crawl_logs IS 'Bảng log quá trình crawl dữ liệu';
COMMENT ON TABLE teams IS 'Bảng lưu trữ thông tin đội bóng';
COMMENT ON TABLE venues IS 'Bảng lưu trữ thông tin sân vận động';

COMMENT ON COLUMN matches.date IS 'Ngày trận đấu theo format YYYYMMDD';
COMMENT ON COLUMN matches.details_crawled IS 'Đánh dấu đã crawl chi tiết trận đấu';
COMMENT ON COLUMN match_details.data IS 'Dữ liệu JSON chi tiết trận đấu';

-- =====================================================
-- 12. CLEANUP (nếu cần)
-- =====================================================

-- Xóa bảng cũ nếu có
-- DROP TABLE IF EXISTS old_table_name CASCADE;

-- =====================================================
-- HOÀN THÀNH
-- =====================================================

-- Kiểm tra các bảng đã tạo
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Kiểm tra các view đã tạo
SELECT 
    schemaname,
    viewname,
    viewowner
FROM pg_views 
WHERE schemaname = 'public' 
ORDER BY viewname;

-- Kiểm tra các index đã tạo
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
ORDER BY tablename, indexname;
