# FotMob Crawler System

Hệ thống crawler tự động thu thập dữ liệu bóng đá từ FotMob với tính năng:

## Tính năng chính

1. **Crawler tự động với CronJob**
   - Crawl matches mỗi 6 giờ cho 2 ngày tới
   - Crawl leagues chỉ 1 lần duy nhất  
   - Xử lý match details qua Redis queue (mỗi 60s)

2. **Database tích hợp**
   - PostgreSQL để lưu trữ dữ liệu
   - Auto-update dữ liệu khi crawl lại
   - Logging đầy đủ các hoạt động crawler

3. **Redis Queue**
   - Queue xử lý match details không đồng bộ
   - Tránh duplicate processing
   - Retry mechanism

4. **FastAPI REST API**
   - CRUD operations cho tất cả dữ liệu
   - Manual trigger crawler
   - Monitor queue status
   - Statistics dashboard

## Cài đặt

### 1. C<PERSON> và setup
```bash
git clone <repo>
cd fotmob-crawler
pip install -r requirements.txt
```

### 2. Setup database và Redis
```bash
# Sử dụng Docker Compose
docker-compose up -d postgres redis

# Hoặc cài đặt riêng PostgreSQL và Redis
```

### 3. Cấu hình
```bash
cp env.example .env
# Chỉnh sửa file .env theo môi trường của bạn
```

### 4. Database migration
```bash
alembic init alembic
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head
```

## Chạy ứng dụng

### 1. Chạy đầy đủ (API + Scheduler)
```bash
python main.py --mode api
```

### 2. Chỉ chạy crawler một lần
```bash
python main.py --mode crawler
```

### 3. Chỉ chạy scheduler
```bash
python main.py --mode scheduler
```

### 4. Chạy với Docker
```bash
docker-compose up -d
```

## API Endpoints

### Health Check
- `GET /health` - Kiểm tra trạng thái hệ thống

### Leagues
- `GET /leagues` - Lấy danh sách leagues
- `GET /leagues/{league_id}` - Chi tiết league

### Matches  
- `GET /matches` - Danh sách matches (có filter)
- `GET /matches/{match_id}` - Chi tiết match

### Crawler Control
- `POST /crawler/run-matches` - Chạy crawler matches thủ công
- `POST /crawler/run-leagues` - Chạy crawler leagues thủ công
- `POST /crawler/process-match-details/{match_id}` - Xử lý match details

### Queue Management
- `GET /queue/status` - Trạng thái queue
- `POST /queue/clear` - Xóa queue

### Monitoring
- `GET /logs` - Xem crawler logs
- `GET /stats` - Thống kê hệ thống

## Cấu trúc Database

### Bảng leagues
- Lưu thông tin leagues (chỉ crawl 1 lần)

### Bảng matches
- Lưu thông tin matches theo ngày
- Auto update khi crawl lại

### Bảng match_details
- Chi tiết match từ Redis queue processing

### Bảng crawl_logs  
- Log tất cả hoạt động crawler

## Scheduler Logic

1. **Matches Crawler**: Chạy mỗi 6h, crawl từ hôm nay đến 2 ngày tới
2. **Match Details Processor**: Chạy liên tục, xử lý queue mỗi 60s
3. **Leagues Crawler**: Chạy 1 lần duy nhất vào 00:00

## Production Deployment

1. Sử dụng Docker Compose cho production
2. Setup nginx reverse proxy
3. Monitor logs và performance
4. Backup database định kỳ

## Troubleshooting

- Kiểm tra logs trong database hoặc console
- Monitor Redis queue length
- Restart scheduler nếu cần thiết
- Verify proxy configuration
## 1. Matches (Danh sách trận đấu)
a. Matches theo ngày

Mục đích: Lấy danh sách các trận đấu diễn ra trong ngày.

Tần suất: 6 giờ/lần, cho hôm nay + 2 ngày tới.

API: /matches?date={YYYYMMDD}&timezone=Asia/Saigon&ccode3=VNM

Dữ liệu chính:

match_id (ID trận đấu)

league_id (thuộc giải nào)

home_team, away_team

kickoff_time

status (Not started, Live, Finished, Postponed)

Odds tổng quát (nếu có)

Lưu: Bảng matches (tự update khi crawl lại).

b. Matches theo giải

Mục đích: Lấy toàn bộ danh sách trận đấu của một giải (tất cả vòng).

Tần suất: Thủ công (force refresh).

API: /leagues?id={league_id}

Dữ liệu chính: như trên nhưng có thêm thông tin vòng đấu.

Lưu: Bảng matches (merge/update).

Ứng dụng: Dùng khi cần backfill toàn bộ lịch sử một giải.

## 2. Match Details (Chi tiết trận đấu)
a. Chi tiết từng trận

Mục đích: Lấy thông tin chi tiết của 1 trận (timeline, stats, lineups).

Tần suất: Worker chạy mỗi 60 giây qua Redis queue.

API: /matchDetails?matchId={match_id}

Dữ liệu chính:

Timeline: bàn thắng, thẻ, thay người, VAR

Stats: xG, sút, chuyền, tranh chấp…

Lineups: đội hình, cầu thủ dự bị, HLV

Odds chi tiết theo provider

Lưu:

Nếu Finished → ghi vào bảng match_details.

Nếu Live → tạm giữ trong Redis để cập nhật tiếp.

b. Chi tiết theo giải

Mục đích: Crawl chi tiết tất cả trận của 1 giải (loop qua match_id).

Tần suất: Thủ công (dùng khi hệ thống mới setup hoặc cần backfill).

Luồng:

Lấy danh sách match_id từ bảng matches của league_id đó.

Gọi API /matchDetails?matchId={...} cho từng trận.

Lưu kết quả vào match_details.