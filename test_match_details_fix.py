#!/usr/bin/env python3
"""
Test script để verify fix cho match_details logic
"""

import json
from database import SessionLocal, MatchDetail
from fotmob_crawler import crawler
from redis_client import redis_client

def test_match_details_fix():
    """Test fix cho match_details logic"""
    print("🧪 Testing match_details fix...")
    
    # Get some finished matches from Redis
    keys = redis_client.list_live_match_ids()
    if not keys:
        print("❌ No match details in Redis to test")
        return
        
    print(f"📊 Found {len(keys)} match details in Redis")
    
    # Test with first 3 matches
    test_matches = []
    for match_key in keys[:3]:
        match_id = match_key.replace('match_details:', '')
        data = redis_client.get_match_details(match_id)
        if data:
            parsed = json.loads(data)
            general = parsed.get('general', {})
            is_finished = general.get('finished', False)
            if is_finished:
                test_matches.append(match_id)
                
    if not test_matches:
        print("❌ No finished matches found in Redis")
        return
        
    print(f"🎯 Testing with {len(test_matches)} finished matches")
    
    # Establish session
    if not crawler.establish_session():
        print("❌ Failed to establish crawler session")
        return
        
    db = SessionLocal()
    try:
        success_count = 0
        for i, match_id in enumerate(test_matches, 1):
            print(f"\n[{i}/{len(test_matches)}] Testing match {match_id}...")
            
            # Check before
            before_db = db.query(MatchDetail).filter(MatchDetail.match_id == match_id).first()
            before_redis = redis_client.get_match_details(match_id)
            
            print(f"   Before: DB={bool(before_db)}, Redis={bool(before_redis)}")
            
            # Crawl with new logic
            success = crawler.crawl_match_details(db, match_id)
            
            # Check after
            after_db = db.query(MatchDetail).filter(MatchDetail.match_id == match_id).first()
            after_redis = redis_client.get_match_details(match_id)
            
            print(f"   After:  DB={bool(after_db)}, Redis={bool(after_redis)}")
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            
            if success and after_db and not after_redis:
                success_count += 1
                print(f"   ✅ Match correctly moved from Redis to DB!")
            elif success and after_redis:
                print(f"   ⚠️ Match still in Redis (might not be finished)")
            else:
                print(f"   ❌ Unexpected result")
                
        print(f"\n🎉 Test completed: {success_count}/{len(test_matches)} matches successfully moved to DB")
        
        # Final stats
        total_db = db.query(MatchDetail).count()
        total_redis = len(redis_client.list_live_match_ids())
        print(f"📊 Final stats: DB={total_db}, Redis={total_redis}")
        
    finally:
        db.close()

if __name__ == "__main__":
    test_match_details_fix()
