#!/usr/bin/env python3
"""
Test script để kiểm tra zpopmax fixes
"""

import redis
from redis_client import redis_client
from config import settings

def test_zpopmax_fixes():
    """Test các fixes cho zpopmax"""
    print("🧪 **Testing zpopmax fixes**")
    print("=" * 50)
    
    try:
        # Clear queue trước khi test
        redis_client.clear_queue()
        print("✅ Cleared queue")
        
        # Thêm test data
        test_matches = [
            ("match_001", 1),
            ("match_002", 2), 
            ("match_003", 3),
            ("match_004", 4),
            ("match_005", 5)
        ]
        
        print(f"📝 Adding {len(test_matches)} test matches...")
        for match_id, priority in test_matches:
            redis_client.add_match_to_queue(match_id, priority=priority)
        
        queue_length = redis_client.get_queue_length()
        print(f"📊 Queue length: {queue_length}")
        
        # Test 1: Pop single match
        print("\n🔍 Test 1: Pop single match")
        single_match = redis_client.get_next_match_from_queue()
        print(f"   Popped: {single_match}")
        print(f"   Queue length after: {redis_client.get_queue_length()}")
        
        # Test 2: Pop batch matches
        print("\n🔍 Test 2: Pop batch matches (2 items)")
        batch_matches = redis_client.pop_matches_from_queue(batch_size=2)
        print(f"   Popped batch: {batch_matches}")
        print(f"   Queue length after: {redis_client.get_queue_length()}")
        
        # Test 3: Pop remaining matches
        print("\n🔍 Test 3: Pop remaining matches")
        remaining_matches = redis_client.pop_matches_from_queue(batch_size=10)
        print(f"   Popped remaining: {remaining_matches}")
        print(f"   Queue length after: {redis_client.get_queue_length()}")
        
        # Test 4: Pop from empty queue
        print("\n🔍 Test 4: Pop from empty queue")
        empty_result = redis_client.get_next_match_from_queue()
        print(f"   Result from empty queue: {empty_result}")
        
        empty_batch = redis_client.pop_matches_from_queue(batch_size=5)
        print(f"   Batch from empty queue: {empty_batch}")
        
        print("\n" + "=" * 50)
        print("✅ All zpopmax tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_redis_version():
    """Kiểm tra Redis version để biết có support zpopmax count không"""
    try:
        r = redis.from_url(settings.REDIS_URL, decode_responses=True)
        info = r.info()
        redis_version = info.get('redis_version', 'unknown')
        print(f"🔧 Redis version: {redis_version}")
        
        # Test zpopmax với count parameter
        test_key = "test_zpopmax_count"
        r.delete(test_key)
        r.zadd(test_key, {"item1": 1, "item2": 2, "item3": 3})
        
        try:
            # Test zpopmax với count (Redis 6.2+)
            result = r.zpopmax(test_key, count=2)
            print(f"✅ zpopmax with count supported: {result}")
        except TypeError:
            print("⚠️  zpopmax with count not supported (Redis < 6.2)")
            # Fallback test
            result = r.zpopmax(test_key)
            print(f"✅ zpopmax without count: {result}")
        
        # Cleanup
        r.delete(test_key)
        
    except Exception as e:
        print(f"❌ Redis version test failed: {e}")

if __name__ == "__main__":
    test_redis_version()
    print()
    test_zpopmax_fixes()
